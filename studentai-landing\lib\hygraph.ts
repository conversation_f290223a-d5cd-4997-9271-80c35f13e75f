import { GraphQLClient } from 'graphql-request'

// Hygraph endpoint and token from environment variables
const endpoint = process.env.HYGRAPH_ENDPOINT || process.env.HYGRAPH_DEV_ENDPOINT
const token = process.env.HYGRAPH_TOKEN || process.env.HYGRAPH_DEV_TOKEN

if (!endpoint) {
  throw new Error('HYGRAPH_ENDPOINT is not defined in environment variables')
}

// Create GraphQL client with authentication
export const hygraphClient = new GraphQLClient(endpoint, {
  headers: {
    ...(token && { Authorization: `Bearer ${token}` }),
  },
})

// Helper function to handle GraphQL requests with error handling
export async function hygraphRequest<T>(
  query: string,
  variables?: Record<string, any>
): Promise<T> {
  try {
    const data = await hygraphClient.request<T>(query, variables)
    return data
  } catch (error) {
    console.error('Hygraph request failed:', error)
    throw new Error(`Failed to fetch data from Hygraph: ${error}`)
  }
}

// Cache configuration for static generation
export const revalidateTime = 60 // Revalidate every 60 seconds
