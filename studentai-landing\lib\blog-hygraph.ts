import { hygraphRequest } from './hygraph'
import {
  GET_BLOG_POSTS,
  GET_BLOG_POST_BY_SLUG,
  GET_FEATURED_BLOG_POSTS,
  GET_RECENT_BLOG_POSTS,
  GET_BLOG_POSTS_BY_CATEGORY,
  GET_BLOG_POSTS_BY_TAG,
  GET_CATEGORIES,
  GET_TAGS,
  SEARCH_BLOG_POSTS,
} from './queries/blog'
import {
  HygraphBlogPost,
  HygraphCategory,
  HygraphTag,
  GetBlogPostsResponse,
  GetBlogPostResponse,
  GetCategoriesResponse,
  GetTagsResponse,
  BlogPostFilters,
} from './types/hygraph'

// Transform Hygraph blog post to match existing interface
export interface Author {
  name: string
  title?: string
  avatar?: string
  bio?: string
  socialLinks?: {
    twitter?: string
    linkedin?: string
    website?: string
  }
}

export interface BlogPost {
  id: string
  title: string
  excerpt: string
  date: string
  author: Author
  content: string
  coverImage?: string
  category: string
  tags: string[]
  featured?: boolean
  slug?: string
  seoTitle?: string
  seoDescription?: string
  readingTime?: number
}

// Transform Hygraph blog post to legacy format
function transformBlogPost(hygraphPost: HygraphBlogPost): BlogPost {
  return {
    id: hygraphPost.id,
    title: hygraphPost.title,
    excerpt: hygraphPost.excerpt,
    date: hygraphPost.publishedAt || hygraphPost.createdAt,
    author: {
      name: hygraphPost.author.name,
      title: hygraphPost.author.title,
      avatar: hygraphPost.author.avatar?.url,
      bio: hygraphPost.author.bio?.text,
      socialLinks: hygraphPost.author.socialLinks,
    },
    content: hygraphPost.content.html,
    coverImage: hygraphPost.coverImage?.url,
    category: hygraphPost.category.name,
    tags: hygraphPost.tags.map(tag => tag.name),
    featured: hygraphPost.featured,
    slug: hygraphPost.slug,
    seoTitle: hygraphPost.seoTitle,
    seoDescription: hygraphPost.seoDescription,
    readingTime: hygraphPost.readingTime,
  }
}

// Get all blog posts
export async function getAllBlogPosts(filters?: BlogPostFilters): Promise<BlogPost[]> {
  try {
    const variables: any = {
      first: 100, // Adjust as needed
      where: {
        published: true,
      },
    }

    // Apply filters
    if (filters?.category) {
      variables.where.category = { slug: filters.category }
    }
    if (filters?.tag) {
      variables.where.tags_some = { slug: filters.tag }
    }
    if (filters?.featured !== undefined) {
      variables.where.featured = filters.featured
    }

    const data = await hygraphRequest<GetBlogPostsResponse>(GET_BLOG_POSTS, variables)
    return data.blogPosts.map(transformBlogPost).sort((a, b) => 
      new Date(b.date).getTime() - new Date(a.date).getTime()
    )
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}

// Get a single blog post by slug
export async function getBlogPost(slug: string): Promise<BlogPost | undefined> {
  try {
    const data = await hygraphRequest<GetBlogPostResponse>(GET_BLOG_POST_BY_SLUG, { slug })
    return data.blogPost ? transformBlogPost(data.blogPost) : undefined
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return undefined
  }
}

// Get featured blog posts
export async function getFeaturedPosts(count = 5): Promise<BlogPost[]> {
  try {
    const data = await hygraphRequest<GetBlogPostsResponse>(GET_FEATURED_BLOG_POSTS, { first: count })
    return data.blogPosts.map(transformBlogPost)
  } catch (error) {
    console.error('Error fetching featured posts:', error)
    return []
  }
}

// Get recent blog posts
export async function getRecentPosts(count = 3, excludeId?: string): Promise<BlogPost[]> {
  try {
    const data = await hygraphRequest<GetBlogPostsResponse>(GET_RECENT_BLOG_POSTS, { 
      first: count,
      excludeId 
    })
    return data.blogPosts.map(transformBlogPost)
  } catch (error) {
    console.error('Error fetching recent posts:', error)
    return []
  }
}

// Get blog posts by category
export async function getBlogPostsByCategory(categorySlug: string, count = 10): Promise<BlogPost[]> {
  try {
    const data = await hygraphRequest<GetBlogPostsResponse>(GET_BLOG_POSTS_BY_CATEGORY, {
      categorySlug,
      first: count,
    })
    return data.blogPosts.map(transformBlogPost)
  } catch (error) {
    console.error('Error fetching posts by category:', error)
    return []
  }
}

// Get blog posts by tag
export async function getBlogPostsByTag(tagSlug: string, count = 10): Promise<BlogPost[]> {
  try {
    const data = await hygraphRequest<GetBlogPostsResponse>(GET_BLOG_POSTS_BY_TAG, {
      tagSlug,
      first: count,
    })
    return data.blogPosts.map(transformBlogPost)
  } catch (error) {
    console.error('Error fetching posts by tag:', error)
    return []
  }
}

// Get all categories
export async function getCategories(): Promise<string[]> {
  try {
    const data = await hygraphRequest<GetCategoriesResponse>(GET_CATEGORIES)
    return data.categories.map(category => category.name)
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

// Get all tags
export async function getTags(): Promise<string[]> {
  try {
    const data = await hygraphRequest<GetTagsResponse>(GET_TAGS)
    return data.tags.map(tag => tag.name)
  } catch (error) {
    console.error('Error fetching tags:', error)
    return []
  }
}

// Search blog posts
export async function searchBlogPosts(searchTerm: string, count = 10): Promise<BlogPost[]> {
  try {
    const data = await hygraphRequest<GetBlogPostsResponse>(SEARCH_BLOG_POSTS, {
      searchTerm,
      first: count,
    })
    return data.blogPosts.map(transformBlogPost)
  } catch (error) {
    console.error('Error searching blog posts:', error)
    return []
  }
}

// Legacy export for backward compatibility
export const blogCategories = [
  "AI Detection", 
  "Academic Integrity", 
  "Writing Tips", 
  "Education", 
  "Content Marketing"
]
