/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"61b191dbbf9c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcU2FuamF5TVxcRGVza3RvcFxcSEFSU0ggIChidGVjaCBjc2UpXFxpbnJlYWxcXHN0dWRlbnRhaS1sYW5kaW5nXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjFiMTkxZGJiZjljXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/seo-utils */ \"(rsc)/./lib/seo-utils.ts\");\n/* harmony import */ var _components_chat_widget__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat-widget */ \"(rsc)/./components/chat-widget.tsx\");\n/* harmony import */ var _components_promo_popup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/promo-popup */ \"(rsc)/./components/promo-popup.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.title,\n        template: \"%s | StudentAIDetector\"\n    },\n    description: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.description,\n    keywords: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.keywords,\n    authors: [\n        {\n            name: \"StudentAIDetector Team\"\n        }\n    ],\n    creator: \"StudentAIDetector\",\n    publisher: \"StudentAIDetector\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(_lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.siteUrl),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        title: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.title,\n        description: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.description,\n        url: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.siteUrl,\n        siteName: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.siteName,\n        locale: \"en_US\",\n        type: \"website\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"StudentAIDetector - AI Text Detection & Humanization Tool\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.title,\n        description: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.description,\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    icons: {\n        icon: [\n            {\n                url: \"/icon.png\",\n                href: \"/icon.png\"\n            }\n        ],\n        shortcut: \"/icon.png\",\n        apple: \"/apple-icon.png\"\n    },\n    manifest: \"/site.webmanifest\",\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    generator: \"v0.dev\"\n};\nfunction RootLayout({ children }) {\n    const homePageSchema = (0,_lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.generateHomePageSchema)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify(homePageSchema)\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify((0,_lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.generateFaqSchema)())\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"Organization\",\n                                name: \"StudentAIDetector\",\n                                url: _lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.siteUrl,\n                                logo: `${_lib_seo_utils__WEBPACK_IMPORTED_MODULE_3__.defaultMetadata.siteUrl}/logo.png`,\n                                sameAs: [\n                                    \"https://twitter.com/studentaidetector\",\n                                    \"https://facebook.com/studentaidetector\",\n                                    \"https://linkedin.com/company/studentaidetector\"\n                                ]\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"system\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_widget__WEBPACK_IMPORTED_MODULE_4__.ChatWidget, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_promo_popup__WEBPACK_IMPORTED_MODULE_5__.PromoPopup, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTYW5qYXlNXFxEZXNrdG9wXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXGlucmVhbFxcc3R1ZGVudGFpLWxhbmRpbmdcXGFwcFxcbG9hZGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZygpIHtcclxuICByZXR1cm4gbnVsbFxyXG59XHJcbiJdLCJuYW1lcyI6WyJMb2FkaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./components/chat-widget.tsx":
/*!************************************!*\
  !*** ./components/chat-widget.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ChatWidget = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ChatWidget() from the server but ChatWidget is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\inreal\\studentai-landing\\components\\chat-widget.tsx",
"ChatWidget",
);

/***/ }),

/***/ "(rsc)/./components/promo-popup.tsx":
/*!************************************!*\
  !*** ./components/promo-popup.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PromoPopup: () => (/* binding */ PromoPopup)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const PromoPopup = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PromoPopup() from the server but PromoPopup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\inreal\\studentai-landing\\components\\promo-popup.tsx",
"PromoPopup",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\HARSH  (btech cse)\\inreal\\studentai-landing\\components\\theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./lib/seo-utils.ts":
/*!**************************!*\
  !*** ./lib/seo-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultMetadata: () => (/* binding */ defaultMetadata),\n/* harmony export */   generateFaqSchema: () => (/* binding */ generateFaqSchema),\n/* harmony export */   generateHomePageSchema: () => (/* binding */ generateHomePageSchema),\n/* harmony export */   generateMetaTags: () => (/* binding */ generateMetaTags),\n/* harmony export */   generateToolSchema: () => (/* binding */ generateToolSchema)\n/* harmony export */ });\nconst defaultMetadata = {\n    title: \"StudentAIDetector - AI Text Detection Tool\",\n    description: \"Detect AI-generated content in student submissions with our advanced AI detection tool. Preserve academic integrity with StudentAIDetector.\",\n    keywords: [\n        \"AI detector\",\n        \"AI content detector\",\n        \"ChatGPT detector\",\n        \"AI text checker\",\n        \"AI humanizer\",\n        \"humanize AI text\",\n        \"academic integrity tool\",\n        \"AI writing detector\",\n        \"bypass AI detection\",\n        \"AI text humanizer\"\n    ],\n    siteUrl: \"http://localhost:3000\" || 0,\n    siteName: \"StudentAIDetector\"\n};\nfunction generateMetaTags({ title = defaultMetadata.title, description = defaultMetadata.description, keywords = defaultMetadata.keywords, canonical, ogType = \"website\", ogImage = \"/og-image.jpg\", ogImageAlt = \"StudentAIDetector logo and interface preview\" }) {\n    const siteUrl = \"http://localhost:3000\" || 0;\n    const fullUrl = canonical ? `${siteUrl}${canonical}` : siteUrl;\n    const fullImageUrl = ogImage.startsWith(\"http\") ? ogImage : `${siteUrl}${ogImage}`;\n    return {\n        title: title,\n        description: description,\n        keywords: keywords,\n        metadataBase: new URL(siteUrl),\n        alternates: {\n            canonical: fullUrl\n        },\n        openGraph: {\n            title: title,\n            description: description,\n            url: fullUrl,\n            siteName: defaultMetadata.siteName,\n            locale: \"en_US\",\n            type: ogType,\n            images: [\n                {\n                    url: fullImageUrl,\n                    width: 1200,\n                    height: 630,\n                    alt: ogImageAlt\n                }\n            ]\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: title,\n            description: description,\n            creator: \"@studentaidetector\",\n            images: [\n                fullImageUrl\n            ]\n        }\n    };\n}\nfunction generateHomePageSchema() {\n    const siteUrl = \"http://localhost:3000\" || 0;\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"WebSite\",\n        \"@id\": `${siteUrl}/#website`,\n        url: siteUrl,\n        name: \"StudentAIDetector\",\n        description: \"Detect AI-generated content in student submissions with our advanced AI detection tool.\",\n        potentialAction: [\n            {\n                \"@type\": \"SearchAction\",\n                target: {\n                    \"@type\": \"EntryPoint\",\n                    urlTemplate: `${siteUrl}/search?q={search_term_string}`\n                },\n                \"query-input\": \"required name=search_term_string\"\n            }\n        ],\n        inLanguage: \"en-US\"\n    };\n}\nfunction generateFaqSchema() {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"FAQPage\",\n        mainEntity: [\n            {\n                \"@type\": \"Question\",\n                name: \"How accurate is StudentAIDetector?\",\n                acceptedAnswer: {\n                    \"@type\": \"Answer\",\n                    text: \"Our AI detection algorithms are continuously improved and currently achieve over 90% accuracy in identifying AI-generated content from major models.\"\n                }\n            },\n            {\n                \"@type\": \"Question\",\n                name: \"Can I use StudentAIDetector for free?\",\n                acceptedAnswer: {\n                    \"@type\": \"Answer\",\n                    text: \"Yes, we offer a free plan that includes basic AI detection features and allows up to 3 analyses per month.\"\n                }\n            },\n            {\n                \"@type\": \"Question\",\n                name: \"What file formats does StudentAIDetector support?\",\n                acceptedAnswer: {\n                    \"@type\": \"Answer\",\n                    text: \"Our basic and pro plans support .txt, .pdf, .doc, and .docx file formats. The free plan supports text input only.\"\n                }\n            },\n            {\n                \"@type\": \"Question\",\n                name: \"How does StudentAIDetector detect AI-generated content?\",\n                acceptedAnswer: {\n                    \"@type\": \"Answer\",\n                    text: \"StudentAIDetector uses a sophisticated multi-layered approach including pattern recognition algorithms, linguistic analysis, contextual evaluation, and comparative benchmarking against known AI models.\"\n                }\n            }\n        ]\n    };\n}\nfunction generateToolSchema(name, description, url) {\n    return {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"SoftwareApplication\",\n        name: name,\n        description: description,\n        url: url,\n        applicationCategory: \"EducationalApplication\",\n        operatingSystem: \"Web\",\n        offers: {\n            \"@type\": \"Offer\",\n            price: \"0\",\n            priceCurrency: \"USD\",\n            availability: \"https://schema.org/Online\"\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/seo-utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\layout.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cchat-widget.tsx%22%2C%22ids%22%3A%5B%22ChatWidget%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cpromo-popup.tsx%22%2C%22ids%22%3A%5B%22PromoPopup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cchat-widget.tsx%22%2C%22ids%22%3A%5B%22ChatWidget%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cpromo-popup.tsx%22%2C%22ids%22%3A%5B%22PromoPopup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/chat-widget.tsx */ \"(rsc)/./components/chat-widget.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/promo-popup.tsx */ \"(rsc)/./components/promo-popup.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cchat-widget.tsx%22%2C%22ids%22%3A%5B%22ChatWidget%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cpromo-popup.tsx%22%2C%22ids%22%3A%5B%22PromoPopup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/chat-widget.tsx":
/*!************************************!*\
  !*** ./components/chat-widget.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatWidget: () => (/* binding */ ChatWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* __next_internal_client_entry_do_not_use__ ChatWidget auto */ \n\n\nfunction ChatWidget() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWidget.useEffect\": ()=>{\n            // Configure chat widget\n            window.ChatWidgetConfig = {\n                webhook: {\n                    url: \"https://n8n-main.uruqqo.easypanel.host/webhook/8eadb998-46cc-4ab2-991f-667792b310ff/chat\",\n                    route: \"general\"\n                },\n                branding: {\n                    logo: \"https://i.postimg.cc/j52qh9gt/Color-Version.png\",\n                    name: \"StudentAIDetector\",\n                    welcomeText: \"👋 Welcome to StudentAIDetector your AI writing checker!\",\n                    responseTimeText: \"Hit the button and let's chat!\"\n                },\n                style: {\n                    primaryColor: \"#1D4ED8\",\n                    secondaryColor: \"#3B82F6\",\n                    position: \"right\",\n                    backgroundColor: \"#ffffff\",\n                    fontColor: \"#111827\"\n                }\n            };\n        }\n    }[\"ChatWidget.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        src: \"https://cdn.jsdelivr.net/gh/funtastic418/chat-widget@main/chat-widget.js\",\n        strategy: \"lazyOnload\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\chat-widget.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/chat-widget.tsx\n");

/***/ }),

/***/ "(ssr)/./components/promo-popup.tsx":
/*!************************************!*\
  !*** ./components/promo-popup.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PromoPopup: () => (/* binding */ PromoPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Gift,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Gift,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Gift,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Gift,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Gift,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ PromoPopup auto */ \n\n\n\n\n\n\nfunction PromoPopup() {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClosing, setIsClosing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [days, setDays] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const [hours, setHours] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(14);\n    const [minutes, setMinutes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(22);\n    // Set discount amount based on pricing strategy\n    const discountPercent = 40;\n    const bonusCredits = 10000;\n    const originalPrice = 29;\n    const discountedPrice = 17;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PromoPopup.useEffect\": ()=>{\n            // Show popup after delay\n            const timer = setTimeout({\n                \"PromoPopup.useEffect.timer\": ()=>{\n                    setIsVisible(true);\n                }\n            }[\"PromoPopup.useEffect.timer\"], 5000);\n            return ({\n                \"PromoPopup.useEffect\": ()=>clearTimeout(timer)\n            })[\"PromoPopup.useEffect\"];\n        }\n    }[\"PromoPopup.useEffect\"], []);\n    // Handle countdown timer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PromoPopup.useEffect\": ()=>{\n            const countdownInterval = setInterval({\n                \"PromoPopup.useEffect.countdownInterval\": ()=>{\n                    if (minutes > 0) {\n                        setMinutes(minutes - 1);\n                    } else {\n                        if (hours > 0) {\n                            setHours(hours - 1);\n                            setMinutes(59);\n                        } else {\n                            if (days > 0) {\n                                setDays(days - 1);\n                                setHours(23);\n                                setMinutes(59);\n                            } else {\n                                clearInterval(countdownInterval);\n                            }\n                        }\n                    }\n                }\n            }[\"PromoPopup.useEffect.countdownInterval\"], 60000); // Update every minute\n            return ({\n                \"PromoPopup.useEffect\": ()=>clearInterval(countdownInterval)\n            })[\"PromoPopup.useEffect\"];\n        }\n    }[\"PromoPopup.useEffect\"], [\n        days,\n        hours,\n        minutes\n    ]);\n    const handleClose = ()=>{\n        setIsClosing(true);\n        setTimeout(()=>{\n            setIsVisible(false);\n            setIsClosing(false);\n        }, 400);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-md\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    scale: 0.8,\n                    opacity: 0\n                },\n                animate: {\n                    scale: isClosing ? 0.8 : 1,\n                    opacity: isClosing ? 0 : 1\n                },\n                transition: {\n                    type: \"spring\",\n                    stiffness: 400,\n                    damping: 30\n                },\n                className: \"relative w-full max-w-5xl overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative rounded-2xl shadow-2xl overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-blue-900/90 via-indigo-900/90 to-purple-900/90 z-10\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 z-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_25%_25%,rgba(79,70,229,0.15)_0%,transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_75%_75%,rgba(147,51,234,0.15)_0%,transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-20\",\n                                    style: {\n                                        backgroundImage: \"linear-gradient(#4f46e5 1px, transparent 1px), linear-gradient(90deg, #4f46e5 1px, transparent 1px)\",\n                                        backgroundSize: \"20px 20px\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-30 flex flex-col md:flex-row items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:w-2/5 relative overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            x: -100,\n                                            opacity: 0\n                                        },\n                                        animate: {\n                                            x: 0,\n                                            opacity: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            type: \"spring\",\n                                            bounce: 0.3\n                                        },\n                                        className: \"p-4 md:p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-[250px] md:h-[400px] w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    src: \"/aidetector.webp\",\n                                                    alt: \"AI Detector Robot\",\n                                                    fill: true,\n                                                    className: \"object-contain\",\n                                                    priority: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                className: \"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-blue-500/30 blur-3xl -z-10\",\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.2,\n                                                        1\n                                                    ],\n                                                    opacity: [\n                                                        0.3,\n                                                        0.5,\n                                                        0.3\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 3,\n                                                    repeat: Infinity,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                initial: {\n                                                    scale: 0.8,\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    scale: 1,\n                                                    opacity: 1\n                                                },\n                                                transition: {\n                                                    delay: 0.6,\n                                                    duration: 0.5\n                                                },\n                                                className: \"absolute top-10 right-10 md:top-6 md:right-6 w-20 h-20 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex flex-col items-center justify-center text-white shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium\",\n                                                        children: \"SAVE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xl font-bold\",\n                                                        children: [\n                                                            discountPercent,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs\",\n                                                        children: \"TODAY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:w-3/5 p-6 md:p-10 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            className: \"flex items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-1 bg-white/20 rounded-full mr-3 backdrop-blur-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-yellow-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium uppercase tracking-wider text-yellow-300 text-sm\",\n                                                    children: \"Limited Time Offer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                y: 30,\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                y: 0,\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-3xl md:text-4xl lg:text-5xl font-bold mb-2 leading-tight\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gradient-to-r from-blue-300 to-purple-200 text-transparent bg-clip-text\",\n                                                        children: \"40% OFF Premium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl font-bold mb-3\",\n                                                    children: [\n                                                        \"+ \",\n                                                        bonusCredits.toLocaleString(),\n                                                        \" Extra Credits\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                y: 20,\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                y: 0,\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.2\n                                            },\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-200 mb-1\",\n                                                    children: \"Offer ends in:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/10 backdrop-blur-sm px-3 py-2 rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-mono font-bold\",\n                                                                    children: days\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs ml-1\",\n                                                                    children: \"days\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/10 backdrop-blur-sm px-3 py-2 rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-mono font-bold\",\n                                                                    children: hours\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs ml-1\",\n                                                                    children: \"hrs\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/10 backdrop-blur-sm px-3 py-2 rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xl font-mono font-bold\",\n                                                                    children: minutes\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs ml-1\",\n                                                                    children: \"min\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                y: 20,\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                y: 0,\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.3\n                                            },\n                                            className: \"mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-baseline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400 line-through text-lg\",\n                                                            children: [\n                                                                \"$\",\n                                                                originalPrice\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-3xl font-bold ml-2\",\n                                                            children: [\n                                                                \"$\",\n                                                                discountedPrice\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm ml-1 text-gray-300\",\n                                                            children: \"/month\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-200 text-sm\",\n                                                    children: \"Billed annually. Cancel anytime.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                y: 20,\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                y: 0,\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.4\n                                            },\n                                            className: \"mb-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-500/20 p-1 rounded mr-3 text-green-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Unlimited AI detection with higher accuracy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-500/20 p-1 rounded mr-3 text-green-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: [\n                                                                    bonusCredits.toLocaleString(),\n                                                                    \" bonus credits for bulk analysis\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-500/20 p-1 rounded mr-3 text-green-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Advanced analytics and reporting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                y: 20,\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                y: 0,\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.5\n                                            },\n                                            className: \"relative mb-6 bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center w-10 h-10 rounded-full bg-yellow-500/20 text-yellow-300 shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                            animate: {\n                                                                rotate: [\n                                                                    0,\n                                                                    360\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 10,\n                                                                repeat: Infinity,\n                                                                ease: \"linear\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-sm\",\n                                                                children: \"BONUS: Refer & Earn More\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-200\",\n                                                                children: \"Get +5,000 free credits for each friend who signs up\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                y: 20,\n                                                opacity: 0\n                                            },\n                                            animate: {\n                                                y: 0,\n                                                opacity: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.6\n                                            },\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/pricing?promo=FALL40\",\n                                                    className: \"block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                        whileHover: {\n                                                            scale: 1.03\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.97\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            className: \"w-full py-6 md:py-7 text-lg md:text-xl bg-gradient-to-r from-blue-400 to-indigo-500 hover:from-blue-500 hover:to-indigo-600 text-white shadow-lg shadow-blue-600/30 transition-all hover:shadow-indigo-500/40 border border-blue-400/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                                                animate: {\n                                                                    x: [\n                                                                        0,\n                                                                        5,\n                                                                        0\n                                                                    ]\n                                                                },\n                                                                transition: {\n                                                                    duration: 1.5,\n                                                                    repeat: Infinity,\n                                                                    repeatDelay: 2\n                                                                },\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    \"Get 40% Off Now\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"ml-2 h-5 w-5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-center text-blue-200\",\n                                                    children: \"Offer valid for new and existing users. No coupon needed.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClose,\n                            className: \"absolute top-4 right-4 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors z-40 backdrop-blur-sm border border-white/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Gift_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-5 h-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\promo-popup.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/promo-popup.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcU2FuamF5TVxcRGVza3RvcFxcSEFSU0ggIChidGVjaCBjc2UpXFxpbnJlYWxcXHN0dWRlbnRhaS1sYW5kaW5nXFxjb21wb25lbnRzXFx0aGVtZS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXHJcblxyXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCdcclxuaW1wb3J0IHtcclxuICBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlcixcclxuICB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyxcclxufSBmcm9tICduZXh0LXRoZW1lcydcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xyXG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxyXG59XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\HARSH  (btech cse)\\\\inreal\\\\studentai-landing\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNhbmpheU1cXERlc2t0b3BcXEhBUlNIICAoYnRlY2ggY3NlKVxcaW5yZWFsXFxzdHVkZW50YWktbGFuZGluZ1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXHJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XHJcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cchat-widget.tsx%22%2C%22ids%22%3A%5B%22ChatWidget%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cpromo-popup.tsx%22%2C%22ids%22%3A%5B%22PromoPopup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cchat-widget.tsx%22%2C%22ids%22%3A%5B%22ChatWidget%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cpromo-popup.tsx%22%2C%22ids%22%3A%5B%22PromoPopup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/chat-widget.tsx */ \"(ssr)/./components/chat-widget.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/promo-popup.tsx */ \"(ssr)/./components/promo-popup.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cchat-widget.tsx%22%2C%22ids%22%3A%5B%22ChatWidget%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Cpromo-popup.tsx%22%2C%22ids%22%3A%5B%22PromoPopup%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NhbmpheU0lNUMlNUNEZXNrdG9wJTVDJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDJTVDaW5yZWFsJTVDJTVDc3R1ZGVudGFpLWxhbmRpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNTYW5qYXlNJTVDJTVDRGVza3RvcCU1QyU1Q0hBUlNIJTIwJTIwKGJ0ZWNoJTIwY3NlKSU1QyU1Q2lucmVhbCU1QyU1Q3N0dWRlbnRhaS1sYW5kaW5nJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDU2FuamF5TSU1QyU1Q0Rlc2t0b3AlNUMlNUNIQVJTSCUyMCUyMChidGVjaCUyMGNzZSklNUMlNUNpbnJlYWwlNUMlNUNzdHVkZW50YWktbGFuZGluZyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NhbmpheU0lNUMlNUNEZXNrdG9wJTVDJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDJTVDaW5yZWFsJTVDJTVDc3R1ZGVudGFpLWxhbmRpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NhbmpheU0lNUMlNUNEZXNrdG9wJTVDJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDJTVDaW5yZWFsJTVDJTVDc3R1ZGVudGFpLWxhbmRpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NhbmpheU0lNUMlNUNEZXNrdG9wJTVDJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDJTVDaW5yZWFsJTVDJTVDc3R1ZGVudGFpLWxhbmRpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NhbmpheU0lNUMlNUNEZXNrdG9wJTVDJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDJTVDaW5yZWFsJTVDJTVDc3R1ZGVudGFpLWxhbmRpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1NhbmpheU0lNUMlNUNEZXNrdG9wJTVDJTVDSEFSU0glMjAlMjAoYnRlY2glMjBjc2UpJTVDJTVDaW5yZWFsJTVDJTVDc3R1ZGVudGFpLWxhbmRpbmclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBNks7QUFDN0s7QUFDQSwwT0FBZ0w7QUFDaEw7QUFDQSwwT0FBZ0w7QUFDaEw7QUFDQSxvUkFBc007QUFDdE07QUFDQSx3T0FBK0s7QUFDL0s7QUFDQSw0UEFBMEw7QUFDMUw7QUFDQSxrUUFBNkw7QUFDN0w7QUFDQSxzUUFBOEwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhbmpheU1cXFxcRGVza3RvcFxcXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXFxcaW5yZWFsXFxcXHN0dWRlbnRhaS1sYW5kaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhbmpheU1cXFxcRGVza3RvcFxcXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXFxcaW5yZWFsXFxcXHN0dWRlbnRhaS1sYW5kaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXNlZ21lbnQuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhbmpheU1cXFxcRGVza3RvcFxcXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXFxcaW5yZWFsXFxcXHN0dWRlbnRhaS1sYW5kaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhbmpheU1cXFxcRGVza3RvcFxcXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXFxcaW5yZWFsXFxcXHN0dWRlbnRhaS1sYW5kaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFNhbmpheU1cXFxcRGVza3RvcFxcXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXFxcaW5yZWFsXFxcXHN0dWRlbnRhaS1sYW5kaW5nXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbGF5b3V0LXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU2FuamF5TVxcXFxEZXNrdG9wXFxcXEhBUlNIICAoYnRlY2ggY3NlKVxcXFxpbnJlYWxcXFxcc3R1ZGVudGFpLWxhbmRpbmdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU2FuamF5TVxcXFxEZXNrdG9wXFxcXEhBUlNIICAoYnRlY2ggY3NlKVxcXFxpbnJlYWxcXFxcc3R1ZGVudGFpLWxhbmRpbmdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcU2FuamF5TVxcXFxEZXNrdG9wXFxcXEhBUlNIICAoYnRlY2ggY3NlKVxcXFxpbnJlYWxcXFxcc3R1ZGVudGFpLWxhbmRpbmdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CSanjayM%5C%5CDesktop%5C%5CHARSH%20%20(btech%20cse)%5C%5Cinreal%5C%5Cstudentai-landing%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/motion-dom","vendor-chunks/@opentelemetry","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CSanjayM%5CDesktop%5CHARSH%20%20(btech%20cse)%5Cinreal%5Cstudentai-landing&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();