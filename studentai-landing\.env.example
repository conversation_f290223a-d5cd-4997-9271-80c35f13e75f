# Hygraph Configuration
# Get these values from your Hygraph project settings
HY<PERSON><PERSON>H_ENDPOINT=https://your-project-region.hygraph.com/v2/your-project-id/master
HYGRAPH_TOKEN=your-permanent-auth-token

# Optional: For development/staging environments
HYGR<PERSON>H_DEV_ENDPOINT=https://your-project-region.hygraph.com/v2/your-project-id/dev
HYGRAPH_DEV_TOKEN=your-dev-auth-token

# Next.js Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
