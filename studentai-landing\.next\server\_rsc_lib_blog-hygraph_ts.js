"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_lib_blog-hygraph_ts";
exports.ids = ["_rsc_lib_blog-hygraph_ts"];
exports.modules = {

/***/ "(rsc)/./lib/blog-hygraph.ts":
/*!*****************************!*\
  !*** ./lib/blog-hygraph.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blogCategories: () => (/* binding */ blogCategories),\n/* harmony export */   getAllBlogPosts: () => (/* binding */ getAllBlogPosts),\n/* harmony export */   getBlogPost: () => (/* binding */ getBlogPost),\n/* harmony export */   getBlogPostsByCategory: () => (/* binding */ getBlogPostsByCategory),\n/* harmony export */   getBlogPostsByTag: () => (/* binding */ getBlogPostsByTag),\n/* harmony export */   getCategories: () => (/* binding */ getCategories),\n/* harmony export */   getFeaturedPosts: () => (/* binding */ getFeaturedPosts),\n/* harmony export */   getRecentPosts: () => (/* binding */ getRecentPosts),\n/* harmony export */   getTags: () => (/* binding */ getTags),\n/* harmony export */   searchBlogPosts: () => (/* binding */ searchBlogPosts)\n/* harmony export */ });\n/* harmony import */ var _hygraph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hygraph */ \"(rsc)/./lib/hygraph.ts\");\n/* harmony import */ var _queries_blog__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queries/blog */ \"(rsc)/./lib/queries/blog.ts\");\n\n\n// Transform Hygraph blog post to legacy format\nfunction transformBlogPost(hygraphPost) {\n    return {\n        id: hygraphPost.id,\n        title: hygraphPost.title,\n        excerpt: hygraphPost.excerpt,\n        date: hygraphPost.publishedAt || hygraphPost.createdAt,\n        author: {\n            name: hygraphPost.author.name,\n            title: hygraphPost.author.title,\n            avatar: hygraphPost.author.avatar?.url,\n            bio: hygraphPost.author.bio?.text,\n            socialLinks: hygraphPost.author.socialLinks\n        },\n        content: hygraphPost.content.html,\n        coverImage: hygraphPost.coverImage?.url,\n        category: hygraphPost.category.name,\n        tags: hygraphPost.tags.map((tag)=>tag.name),\n        featured: hygraphPost.featured,\n        slug: hygraphPost.slug,\n        seoTitle: hygraphPost.seoTitle,\n        seoDescription: hygraphPost.seoDescription,\n        readingTime: hygraphPost.readingTime\n    };\n}\n// Get all blog posts\nasync function getAllBlogPosts(filters) {\n    try {\n        const variables = {\n            first: 100,\n            where: {\n                published: true\n            }\n        };\n        // Apply filters\n        if (filters?.category) {\n            variables.where.category = {\n                slug: filters.category\n            };\n        }\n        if (filters?.tag) {\n            variables.where.tags_some = {\n                slug: filters.tag\n            };\n        }\n        if (filters?.featured !== undefined) {\n            variables.where.featured = filters.featured;\n        }\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_BLOG_POSTS, variables);\n        return data.blogPosts.map(transformBlogPost).sort((a, b)=>new Date(b.date).getTime() - new Date(a.date).getTime());\n    } catch (error) {\n        console.error('Error fetching blog posts:', error);\n        return [];\n    }\n}\n// Get a single blog post by slug\nasync function getBlogPost(slug) {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_BLOG_POST_BY_SLUG, {\n            slug\n        });\n        return data.blogPost ? transformBlogPost(data.blogPost) : undefined;\n    } catch (error) {\n        console.error('Error fetching blog post:', error);\n        return undefined;\n    }\n}\n// Get featured blog posts\nasync function getFeaturedPosts(count = 5) {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_FEATURED_BLOG_POSTS, {\n            first: count\n        });\n        return data.blogPosts.map(transformBlogPost);\n    } catch (error) {\n        console.error('Error fetching featured posts:', error);\n        return [];\n    }\n}\n// Get recent blog posts\nasync function getRecentPosts(count = 3, excludeId) {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_RECENT_BLOG_POSTS, {\n            first: count,\n            excludeId\n        });\n        return data.blogPosts.map(transformBlogPost);\n    } catch (error) {\n        console.error('Error fetching recent posts:', error);\n        return [];\n    }\n}\n// Get blog posts by category\nasync function getBlogPostsByCategory(categorySlug, count = 10) {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_BLOG_POSTS_BY_CATEGORY, {\n            categorySlug,\n            first: count\n        });\n        return data.blogPosts.map(transformBlogPost);\n    } catch (error) {\n        console.error('Error fetching posts by category:', error);\n        return [];\n    }\n}\n// Get blog posts by tag\nasync function getBlogPostsByTag(tagSlug, count = 10) {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_BLOG_POSTS_BY_TAG, {\n            tagSlug,\n            first: count\n        });\n        return data.blogPosts.map(transformBlogPost);\n    } catch (error) {\n        console.error('Error fetching posts by tag:', error);\n        return [];\n    }\n}\n// Get all categories\nasync function getCategories() {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_CATEGORIES);\n        return data.categories.map((category)=>category.name);\n    } catch (error) {\n        console.error('Error fetching categories:', error);\n        return [];\n    }\n}\n// Get all tags\nasync function getTags() {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.GET_TAGS);\n        return data.tags.map((tag)=>tag.name);\n    } catch (error) {\n        console.error('Error fetching tags:', error);\n        return [];\n    }\n}\n// Search blog posts\nasync function searchBlogPosts(searchTerm, count = 10) {\n    try {\n        const data = await (0,_hygraph__WEBPACK_IMPORTED_MODULE_0__.hygraphRequest)(_queries_blog__WEBPACK_IMPORTED_MODULE_1__.SEARCH_BLOG_POSTS, {\n            searchTerm,\n            first: count\n        });\n        return data.blogPosts.map(transformBlogPost);\n    } catch (error) {\n        console.error('Error searching blog posts:', error);\n        return [];\n    }\n}\n// Legacy export for backward compatibility\nconst blogCategories = [\n    \"AI Detection\",\n    \"Academic Integrity\",\n    \"Writing Tips\",\n    \"Education\",\n    \"Content Marketing\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/blog-hygraph.ts\n");

/***/ }),

/***/ "(rsc)/./lib/hygraph.ts":
/*!************************!*\
  !*** ./lib/hygraph.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hygraphClient: () => (/* binding */ hygraphClient),\n/* harmony export */   hygraphRequest: () => (/* binding */ hygraphRequest),\n/* harmony export */   revalidateTime: () => (/* binding */ revalidateTime)\n/* harmony export */ });\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! graphql-request */ \"(rsc)/./node_modules/graphql-request/build/entrypoints/main.js\");\n\n// Hygraph endpoint and token from environment variables\nconst endpoint = process.env.HYGRAPH_ENDPOINT || process.env.HYGRAPH_DEV_ENDPOINT;\nconst token = process.env.HYGRAPH_TOKEN || process.env.HYGRAPH_DEV_TOKEN;\nif (!endpoint) {\n    throw new Error('HYGRAPH_ENDPOINT is not defined in environment variables');\n}\n// Create GraphQL client with authentication\nconst hygraphClient = new graphql_request__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(endpoint, {\n    headers: {\n        ...token && {\n            Authorization: `Bearer ${token}`\n        }\n    }\n});\n// Helper function to handle GraphQL requests with error handling\nasync function hygraphRequest(query, variables) {\n    try {\n        const data = await hygraphClient.request(query, variables);\n        return data;\n    } catch (error) {\n        console.error('Hygraph request failed:', error);\n        throw new Error(`Failed to fetch data from Hygraph: ${error}`);\n    }\n}\n// Cache configuration for static generation\nconst revalidateTime = 60 // Revalidate every 60 seconds\n;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/hygraph.ts\n");

/***/ }),

/***/ "(rsc)/./lib/queries/blog.ts":
/*!*****************************!*\
  !*** ./lib/queries/blog.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ASSET_FRAGMENT: () => (/* binding */ ASSET_FRAGMENT),\n/* harmony export */   AUTHOR_FRAGMENT: () => (/* binding */ AUTHOR_FRAGMENT),\n/* harmony export */   BLOG_POST_FRAGMENT: () => (/* binding */ BLOG_POST_FRAGMENT),\n/* harmony export */   CATEGORY_FRAGMENT: () => (/* binding */ CATEGORY_FRAGMENT),\n/* harmony export */   GET_BLOG_POSTS: () => (/* binding */ GET_BLOG_POSTS),\n/* harmony export */   GET_BLOG_POSTS_BY_CATEGORY: () => (/* binding */ GET_BLOG_POSTS_BY_CATEGORY),\n/* harmony export */   GET_BLOG_POSTS_BY_TAG: () => (/* binding */ GET_BLOG_POSTS_BY_TAG),\n/* harmony export */   GET_BLOG_POST_BY_SLUG: () => (/* binding */ GET_BLOG_POST_BY_SLUG),\n/* harmony export */   GET_CATEGORIES: () => (/* binding */ GET_CATEGORIES),\n/* harmony export */   GET_FEATURED_BLOG_POSTS: () => (/* binding */ GET_FEATURED_BLOG_POSTS),\n/* harmony export */   GET_RECENT_BLOG_POSTS: () => (/* binding */ GET_RECENT_BLOG_POSTS),\n/* harmony export */   GET_TAGS: () => (/* binding */ GET_TAGS),\n/* harmony export */   SEARCH_BLOG_POSTS: () => (/* binding */ SEARCH_BLOG_POSTS),\n/* harmony export */   TAG_FRAGMENT: () => (/* binding */ TAG_FRAGMENT)\n/* harmony export */ });\n// Fragment for Asset fields\nconst ASSET_FRAGMENT = `\n  fragment AssetFragment on Asset {\n    id\n    url\n    fileName\n    mimeType\n    size\n    width\n    height\n    alt\n  }\n`;\n// Fragment for Author fields\nconst AUTHOR_FRAGMENT = `\n  fragment AuthorFragment on Author {\n    id\n    name\n    title\n    bio {\n      html\n      text\n      markdown\n    }\n    avatar {\n      ...AssetFragment\n    }\n    email\n    socialLinks\n    createdAt\n    updatedAt\n  }\n`;\n// Fragment for Category fields\nconst CATEGORY_FRAGMENT = `\n  fragment CategoryFragment on Category {\n    id\n    name\n    slug\n    description\n    color\n    createdAt\n    updatedAt\n  }\n`;\n// Fragment for Tag fields\nconst TAG_FRAGMENT = `\n  fragment TagFragment on Tag {\n    id\n    name\n    slug\n    createdAt\n    updatedAt\n  }\n`;\n// Fragment for Blog Post fields\nconst BLOG_POST_FRAGMENT = `\n  fragment BlogPostFragment on BlogPost {\n    id\n    title\n    slug\n    excerpt\n    content {\n      html\n      text\n      markdown\n    }\n    coverImage {\n      ...AssetFragment\n    }\n    author {\n      ...AuthorFragment\n    }\n    category {\n      ...CategoryFragment\n    }\n    tags {\n      ...TagFragment\n    }\n    featured\n    published\n    publishedAt\n    seoTitle\n    seoDescription\n    readingTime\n    createdAt\n    updatedAt\n  }\n`;\n// Query to get all blog posts\nconst GET_BLOG_POSTS = `\n  ${ASSET_FRAGMENT}\n  ${AUTHOR_FRAGMENT}\n  ${CATEGORY_FRAGMENT}\n  ${TAG_FRAGMENT}\n  ${BLOG_POST_FRAGMENT}\n  \n  query GetBlogPosts(\n    $first: Int = 10\n    $skip: Int = 0\n    $orderBy: BlogPostOrderByInput = publishedAt_DESC\n    $where: BlogPostWhereInput\n  ) {\n    blogPosts(\n      first: $first\n      skip: $skip\n      orderBy: $orderBy\n      where: $where\n    ) {\n      ...BlogPostFragment\n    }\n  }\n`;\n// Query to get a single blog post by slug\nconst GET_BLOG_POST_BY_SLUG = `\n  ${ASSET_FRAGMENT}\n  ${AUTHOR_FRAGMENT}\n  ${CATEGORY_FRAGMENT}\n  ${TAG_FRAGMENT}\n  ${BLOG_POST_FRAGMENT}\n  \n  query GetBlogPostBySlug($slug: String!) {\n    blogPost(where: { slug: $slug }) {\n      ...BlogPostFragment\n    }\n  }\n`;\n// Query to get featured blog posts\nconst GET_FEATURED_BLOG_POSTS = `\n  ${ASSET_FRAGMENT}\n  ${AUTHOR_FRAGMENT}\n  ${CATEGORY_FRAGMENT}\n  ${TAG_FRAGMENT}\n  ${BLOG_POST_FRAGMENT}\n  \n  query GetFeaturedBlogPosts($first: Int = 5) {\n    blogPosts(\n      first: $first\n      where: { featured: true, published: true }\n      orderBy: publishedAt_DESC\n    ) {\n      ...BlogPostFragment\n    }\n  }\n`;\n// Query to get recent blog posts\nconst GET_RECENT_BLOG_POSTS = `\n  ${ASSET_FRAGMENT}\n  ${AUTHOR_FRAGMENT}\n  ${CATEGORY_FRAGMENT}\n  ${TAG_FRAGMENT}\n  ${BLOG_POST_FRAGMENT}\n\n  query GetRecentBlogPosts($first: Int = 5, $excludeId: ID) {\n    blogPosts(\n      first: $first\n      where: { published: true, id_not: $excludeId }\n      orderBy: publishedAt_DESC\n    ) {\n      ...BlogPostFragment\n    }\n  }\n`;\n// Query to get blog posts by category\nconst GET_BLOG_POSTS_BY_CATEGORY = `\n  ${ASSET_FRAGMENT}\n  ${AUTHOR_FRAGMENT}\n  ${CATEGORY_FRAGMENT}\n  ${TAG_FRAGMENT}\n  ${BLOG_POST_FRAGMENT}\n\n  query GetBlogPostsByCategory(\n    $categorySlug: String!\n    $first: Int = 10\n    $skip: Int = 0\n  ) {\n    blogPosts(\n      first: $first\n      skip: $skip\n      where: { published: true, category: { slug: $categorySlug } }\n      orderBy: publishedAt_DESC\n    ) {\n      ...BlogPostFragment\n    }\n  }\n`;\n// Query to get blog posts by tag\nconst GET_BLOG_POSTS_BY_TAG = `\n  ${ASSET_FRAGMENT}\n  ${AUTHOR_FRAGMENT}\n  ${CATEGORY_FRAGMENT}\n  ${TAG_FRAGMENT}\n  ${BLOG_POST_FRAGMENT}\n\n  query GetBlogPostsByTag(\n    $tagSlug: String!\n    $first: Int = 10\n    $skip: Int = 0\n  ) {\n    blogPosts(\n      first: $first\n      skip: $skip\n      where: { published: true, tags_some: { slug: $tagSlug } }\n      orderBy: publishedAt_DESC\n    ) {\n      ...BlogPostFragment\n    }\n  }\n`;\n// Query to get all categories\nconst GET_CATEGORIES = `\n  ${CATEGORY_FRAGMENT}\n\n  query GetCategories {\n    categories(orderBy: name_ASC) {\n      ...CategoryFragment\n    }\n  }\n`;\n// Query to get all tags\nconst GET_TAGS = `\n  ${TAG_FRAGMENT}\n\n  query GetTags {\n    tags(orderBy: name_ASC) {\n      ...TagFragment\n    }\n  }\n`;\n// Query to search blog posts\nconst SEARCH_BLOG_POSTS = `\n  ${ASSET_FRAGMENT}\n  ${AUTHOR_FRAGMENT}\n  ${CATEGORY_FRAGMENT}\n  ${TAG_FRAGMENT}\n  ${BLOG_POST_FRAGMENT}\n\n  query SearchBlogPosts(\n    $searchTerm: String!\n    $first: Int = 10\n    $skip: Int = 0\n  ) {\n    blogPosts(\n      first: $first\n      skip: $skip\n      where: {\n        published: true\n        OR: [\n          { title_contains: $searchTerm }\n          { excerpt_contains: $searchTerm }\n          { content_contains: $searchTerm }\n        ]\n      }\n      orderBy: publishedAt_DESC\n    ) {\n      ...BlogPostFragment\n    }\n  }\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/queries/blog.ts\n");

/***/ })

};
;