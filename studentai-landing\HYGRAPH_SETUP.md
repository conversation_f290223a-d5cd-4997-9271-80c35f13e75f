# Hygraph CMS Integration Guide

This guide explains how to set up and configure Hygraph (formerly GraphCMS) as a headless CMS for managing blog content in the StudentAI Landing project.

## Overview

The project now supports both static blog content (fallback) and dynamic content from Hygraph CMS. When Hygraph is properly configured, the application will fetch content from the CMS. If Hygraph is unavailable or not configured, it falls back to static content.

## Hygraph Setup

### 1. Create a Hygraph Project

1. Go to [Hygraph.com](https://hygraph.com) and create an account
2. Create a new project
3. Choose your preferred region for optimal performance

### 2. Create Content Models

Create the following models in your Hygraph project:

#### Author Model
- **Model Name**: `Author`
- **API ID**: `Author`
- **Fields**:
  - `name` (Single line text, Required)
  - `title` (Single line text)
  - `bio` (Rich text)
  - `avatar` (Asset - Single)
  - `email` (Single line text)
  - `socialLinks` (JSON)

#### Category Model
- **Model Name**: `Category`
- **API ID**: `Category`
- **Fields**:
  - `name` (Single line text, Required)
  - `slug` (Single line text, Required, Unique)
  - `description` (Multi-line text)
  - `color` (Single line text)

#### Tag Model
- **Model Name**: `Tag`
- **API ID**: `Tag`
- **Fields**:
  - `name` (Single line text, Required)
  - `slug` (Single line text, Required, Unique)

#### BlogPost Model
- **Model Name**: `BlogPost`
- **API ID**: `BlogPost`
- **Fields**:
  - `title` (Single line text, Required)
  - `slug` (Single line text, Required, Unique)
  - `excerpt` (Multi-line text, Required)
  - `content` (Rich text, Required)
  - `coverImage` (Asset - Single)
  - `author` (Reference - Author, Required)
  - `category` (Reference - Category, Required)
  - `tags` (Reference - Tag, Multiple)
  - `featured` (Boolean, Default: false)
  - `published` (Boolean, Default: false)
  - `publishedAt` (Date & time)
  - `seoTitle` (Single line text)
  - `seoDescription` (Multi-line text)
  - `readingTime` (Number)

### 3. Configure Permissions

1. Go to **Settings** > **API Access**
2. Create a new **Permanent Auth Token**
3. Set permissions for **Content API**:
   - Read access to all models
   - Read access to assets

### 4. Environment Configuration

Copy the `.env.example` file to `.env.local` and update with your Hygraph credentials:

```bash
# Hygraph Configuration
HYGRAPH_ENDPOINT=https://your-region.hygraph.com/v2/your-project-id/master
HYGRAPH_TOKEN=your-permanent-auth-token

# Optional: Development environment
HYGRAPH_DEV_ENDPOINT=https://your-region.hygraph.com/v2/your-project-id/dev
HYGRAPH_DEV_TOKEN=your-dev-auth-token
```

## Content Management

### Creating Content

1. **Authors**: Create author profiles with bio, avatar, and social links
2. **Categories**: Create categories like "AI Detection", "Academic Integrity", etc.
3. **Tags**: Create relevant tags for content organization
4. **Blog Posts**: Create blog posts with rich content, proper SEO fields, and relationships

### Content Guidelines

- **Slugs**: Use URL-friendly slugs (lowercase, hyphens instead of spaces)
- **SEO**: Always fill SEO title and description for better search visibility
- **Images**: Upload high-quality cover images (recommended: 1200x630px)
- **Publishing**: Set `published` to true and add `publishedAt` date for live content

## Development

### File Structure

```
lib/
├── hygraph.ts              # GraphQL client configuration
├── blog-hygraph.ts         # Hygraph-powered blog functions
├── blog.ts                 # Main blog functions with fallback
├── types/
│   └── hygraph.ts          # TypeScript types for Hygraph data
└── queries/
    └── blog.ts             # GraphQL queries

components/blog/
├── blog-loading.tsx        # Loading skeletons
└── blog-error.tsx          # Error handling components
```

### Key Features

- **Automatic Fallback**: Falls back to static content if Hygraph is unavailable
- **Type Safety**: Full TypeScript support with proper types
- **Error Handling**: Graceful error handling with user-friendly messages
- **Loading States**: Skeleton loading components for better UX
- **SEO Optimization**: Proper meta tags and structured data

### Testing

1. **With Hygraph**: Configure environment variables and test CMS integration
2. **Without Hygraph**: Remove environment variables to test fallback behavior
3. **Error Scenarios**: Test network failures and invalid configurations

## Deployment

### Environment Variables

Ensure the following environment variables are set in your deployment platform:

- `HYGRAPH_ENDPOINT`: Your Hygraph API endpoint
- `HYGRAPH_TOKEN`: Your permanent auth token

### Caching

The application includes built-in caching with revalidation every 60 seconds. Adjust the `revalidateTime` in `lib/hygraph.ts` as needed.

## Troubleshooting

### Common Issues

1. **"HYGRAPH_ENDPOINT is not defined"**: Check environment variables
2. **GraphQL errors**: Verify your schema matches the defined types
3. **Permission errors**: Ensure your auth token has proper read permissions
4. **Content not appearing**: Check that content is published and `published` field is true

### Debug Mode

Enable debug logging by adding to your environment:
```bash
DEBUG=hygraph:*
```

## Migration from Static Content

The current static blog posts can be migrated to Hygraph:

1. Export existing posts from `lib/blog.ts`
2. Create corresponding entries in Hygraph
3. Update slugs to match existing URLs
4. Test thoroughly before removing static fallbacks

## Support

For issues related to:
- **Hygraph Platform**: Check [Hygraph Documentation](https://hygraph.com/docs)
- **Integration Code**: Review the implementation in `lib/blog-hygraph.ts`
- **GraphQL Queries**: Validate queries in Hygraph's GraphQL Playground
