/**
 * Test script for Hygraph integration
 * Run this to verify that the Hygraph setup is working correctly
 */

import { getAllBlogPosts, getBlogPost, getFeaturedPosts } from './blog'

export async function testHygraphIntegration() {
  console.log('🧪 Testing Hygraph Integration...\n')

  try {
    // Test 1: Get all blog posts
    console.log('📝 Testing getAllBlogPosts...')
    const allPosts = await getAllBlogPosts()
    console.log(`✅ Found ${allPosts.length} blog posts`)
    
    if (allPosts.length > 0) {
      const firstPost = allPosts[0]
      console.log(`   First post: "${firstPost.title}"`)
      console.log(`   Author: ${firstPost.author.name}`)
      console.log(`   Category: ${firstPost.category}`)
      console.log(`   Tags: ${firstPost.tags.join(', ')}`)
    }
    console.log('')

    // Test 2: Get featured posts
    console.log('⭐ Testing getFeaturedPosts...')
    const featuredPosts = await getFeaturedPosts()
    console.log(`✅ Found ${featuredPosts.length} featured posts`)
    console.log('')

    // Test 3: Get a specific post (if any exist)
    if (allPosts.length > 0) {
      const testSlug = allPosts[0].slug || allPosts[0].id
      console.log(`🔍 Testing getBlogPost with slug/id: "${testSlug}"...`)
      const singlePost = await getBlogPost(testSlug)
      
      if (singlePost) {
        console.log(`✅ Successfully retrieved post: "${singlePost.title}"`)
        console.log(`   Content length: ${singlePost.content.length} characters`)
        console.log(`   Cover image: ${singlePost.coverImage ? 'Yes' : 'No'}`)
      } else {
        console.log(`❌ Failed to retrieve post with slug/id: "${testSlug}"`)
      }
      console.log('')
    }

    // Test 4: Check data source
    const isHygraphConfigured = !!(process.env.HYGRAPH_ENDPOINT || process.env.HYGRAPH_DEV_ENDPOINT)
    console.log(`🔧 Data source: ${isHygraphConfigured ? 'Hygraph CMS' : 'Static fallback'}`)
    
    if (isHygraphConfigured) {
      console.log(`   Endpoint: ${process.env.HYGRAPH_ENDPOINT || process.env.HYGRAPH_DEV_ENDPOINT}`)
      console.log(`   Token configured: ${!!(process.env.HYGRAPH_TOKEN || process.env.HYGRAPH_DEV_TOKEN)}`)
    }

    console.log('\n🎉 Hygraph integration test completed successfully!')
    return true

  } catch (error) {
    console.error('❌ Hygraph integration test failed:', error)
    console.log('\n💡 Troubleshooting tips:')
    console.log('   1. Check your environment variables in .env.local')
    console.log('   2. Verify your Hygraph endpoint and token')
    console.log('   3. Ensure your content models match the expected schema')
    console.log('   4. Check that you have published content in Hygraph')
    return false
  }
}

// Helper function to run the test from the command line
export async function runTest() {
  const success = await testHygraphIntegration()
  process.exit(success ? 0 : 1)
}

// Run test if this file is executed directly
if (require.main === module) {
  runTest()
}
