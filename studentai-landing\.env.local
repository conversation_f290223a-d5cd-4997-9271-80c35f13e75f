# Hygraph Configuration
# Replace these with your actual Hygraph project values
# HY<PERSON><PERSON>H_ENDPOINT=https://your-project-region.hygraph.com/v2/your-project-id/master
# HYGRAPH_TOKEN=your-permanent-auth-token

# Uncomment and fill these when you have Hygraph setup:
# HYGRAPH_ENDPOINT=https://ap-south-1.hygraph.com/v2/your-project-id/master
# HYGRAPH_TOKEN=your-actual-token-here

# Optional: For development/staging environments
HYGRAPH_DEV_ENDPOINT=https://your-project-region.hygraph.com/v2/your-project-id/dev
HYGRAPH_DEV_TOKEN=your-dev-auth-token

# Next.js Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
