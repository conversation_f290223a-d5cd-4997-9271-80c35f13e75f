// Hygraph Asset type for images and files
export interface HygraphAsset {
  id: string
  url: string
  fileName: string
  mimeType: string
  size: number
  width?: number
  height?: number
  alt?: string
}

// Rich Text content from Hygraph
export interface HygraphRichText {
  html: string
  text: string
  markdown: string
}

// Author model in Hygraph
export interface HygraphAuthor {
  id: string
  name: string
  title?: string
  bio?: HygraphRichText
  avatar?: HygraphAsset
  email?: string
  socialLinks?: {
    twitter?: string
    linkedin?: string
    website?: string
  }
  createdAt: string
  updatedAt: string
}

// Category model in Hygraph
export interface HygraphCategory {
  id: string
  name: string
  slug: string
  description?: string
  color?: string
  createdAt: string
  updatedAt: string
}

// Tag model in Hygraph
export interface HygraphTag {
  id: string
  name: string
  slug: string
  createdAt: string
  updatedAt: string
}

// Blog Post model in Hygraph
export interface HygraphBlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: HygraphRichText
  coverImage?: HygraphAsset
  author: HygraphAuthor
  category: HygraphCategory
  tags: HygraphTag[]
  featured: boolean
  published: boolean
  publishedAt?: string
  seoTitle?: string
  seoDescription?: string
  readingTime?: number
  createdAt: string
  updatedAt: string
}

// Response types for GraphQL queries
export interface GetBlogPostsResponse {
  blogPosts: HygraphBlogPost[]
}

export interface GetBlogPostResponse {
  blogPost: HygraphBlogPost
}

export interface GetCategoriesResponse {
  categories: HygraphCategory[]
}

export interface GetTagsResponse {
  tags: HygraphTag[]
}

export interface GetAuthorsResponse {
  authors: HygraphAuthor[]
}

// Pagination types
export interface PaginationInfo {
  hasNextPage: boolean
  hasPreviousPage: boolean
  pageSize: number
  total: number
}

export interface PaginatedBlogPostsResponse {
  blogPosts: HygraphBlogPost[]
  blogPostsConnection: {
    pageInfo: PaginationInfo
  }
}

// Search and filter types
export interface BlogPostFilters {
  category?: string
  tag?: string
  author?: string
  featured?: boolean
  published?: boolean
  search?: string
}

export interface BlogPostSortOptions {
  field: 'publishedAt' | 'createdAt' | 'updatedAt' | 'title'
  order: 'ASC' | 'DESC'
}
