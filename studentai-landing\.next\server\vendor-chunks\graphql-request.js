"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/graphql-request";
exports.ids = ["vendor-chunks/graphql-request"];
exports.modules = {

/***/ "(rsc)/./node_modules/graphql-request/build/entrypoints/main.js":
/*!****************************************************************!*\
  !*** ./node_modules/graphql-request/build/entrypoints/main.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientError: () => (/* reexport safe */ _legacy_classes_ClientError_js__WEBPACK_IMPORTED_MODULE_0__.ClientError),\n/* harmony export */   GraphQLClient: () => (/* reexport safe */ _legacy_classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_2__.GraphQLClient),\n/* harmony export */   analyzeDocument: () => (/* reexport safe */ _legacy_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_6__.analyzeDocument),\n/* harmony export */   batchRequests: () => (/* reexport safe */ _legacy_functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_3__.batchRequests),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   gql: () => (/* reexport safe */ _legacy_functions_gql_js__WEBPACK_IMPORTED_MODULE_4__.gql),\n/* harmony export */   rawRequest: () => (/* reexport safe */ _legacy_functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_5__.rawRequest),\n/* harmony export */   request: () => (/* reexport safe */ _legacy_functions_request_js__WEBPACK_IMPORTED_MODULE_1__.request)\n/* harmony export */ });\n/* harmony import */ var _legacy_classes_ClientError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../legacy/classes/ClientError.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/classes/ClientError.js\");\n/* harmony import */ var _legacy_functions_request_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../legacy/functions/request.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/functions/request.js\");\n/* harmony import */ var _legacy_classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../legacy/classes/GraphQLClient.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n/* harmony import */ var _legacy_functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../legacy/functions/batchRequests.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js\");\n/* harmony import */ var _legacy_functions_gql_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../legacy/functions/gql.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/functions/gql.js\");\n/* harmony import */ var _legacy_functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../legacy/functions/rawRequest.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js\");\n/* harmony import */ var _legacy_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../legacy/helpers/analyzeDocument.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js\");\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_legacy_functions_request_js__WEBPACK_IMPORTED_MODULE_1__.request);\n//# sourceMappingURL=main.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC1yZXF1ZXN0L2J1aWxkL2VudHJ5cG9pbnRzL21haW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQStEO0FBQ047QUFDVTtBQUNFO0FBQ3BCO0FBQ2M7QUFDUTtBQUN0QztBQUNqQyxpRUFBZSxpRUFBTyxFQUFDO0FBQ3ZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNhbmpheU1cXERlc2t0b3BcXEhBUlNIICAoYnRlY2ggY3NlKVxcaW5yZWFsXFxzdHVkZW50YWktbGFuZGluZ1xcbm9kZV9tb2R1bGVzXFxncmFwaHFsLXJlcXVlc3RcXGJ1aWxkXFxlbnRyeXBvaW50c1xcbWFpbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDbGllbnRFcnJvciB9IGZyb20gJy4uL2xlZ2FjeS9jbGFzc2VzL0NsaWVudEVycm9yLmpzJztcbmltcG9ydCB7IHJlcXVlc3QgfSBmcm9tICcuLi9sZWdhY3kvZnVuY3Rpb25zL3JlcXVlc3QuanMnO1xuZXhwb3J0IHsgR3JhcGhRTENsaWVudCB9IGZyb20gJy4uL2xlZ2FjeS9jbGFzc2VzL0dyYXBoUUxDbGllbnQuanMnO1xuZXhwb3J0IHsgYmF0Y2hSZXF1ZXN0cyB9IGZyb20gJy4uL2xlZ2FjeS9mdW5jdGlvbnMvYmF0Y2hSZXF1ZXN0cy5qcyc7XG5leHBvcnQgeyBncWwgfSBmcm9tICcuLi9sZWdhY3kvZnVuY3Rpb25zL2dxbC5qcyc7XG5leHBvcnQgeyByYXdSZXF1ZXN0IH0gZnJvbSAnLi4vbGVnYWN5L2Z1bmN0aW9ucy9yYXdSZXF1ZXN0LmpzJztcbmV4cG9ydCB7IGFuYWx5emVEb2N1bWVudCB9IGZyb20gJy4uL2xlZ2FjeS9oZWxwZXJzL2FuYWx5emVEb2N1bWVudC5qcyc7XG5leHBvcnQgeyBDbGllbnRFcnJvciwgcmVxdWVzdCwgfTtcbmV4cG9ydCBkZWZhdWx0IHJlcXVlc3Q7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYWluLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/entrypoints/main.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/classes/ClientError.js":
/*!**************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/classes/ClientError.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientError: () => (/* binding */ ClientError)\n/* harmony export */ });\nclass ClientError extends Error {\n    response;\n    request;\n    constructor(response, request) {\n        const message = `${ClientError.extractMessage(response)}: ${JSON.stringify({\n            response,\n            request,\n        })}`;\n        super(message);\n        Object.setPrototypeOf(this, ClientError.prototype);\n        this.response = response;\n        this.request = request;\n        // this is needed as Safari doesn't support .captureStackTrace\n        if (typeof Error.captureStackTrace === `function`) {\n            Error.captureStackTrace(this, ClientError);\n        }\n    }\n    static extractMessage(response) {\n        return response.errors?.[0]?.message ?? `GraphQL Error (Code: ${String(response.status)})`;\n    }\n}\n//# sourceMappingURL=ClientError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC1yZXF1ZXN0L2J1aWxkL2xlZ2FjeS9jbGFzc2VzL0NsaWVudEVycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixxQ0FBcUMsSUFBSTtBQUNwRTtBQUNBO0FBQ0EsU0FBUyxFQUFFO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3RUFBd0Usd0JBQXdCO0FBQ2hHO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTYW5qYXlNXFxEZXNrdG9wXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXGlucmVhbFxcc3R1ZGVudGFpLWxhbmRpbmdcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbC1yZXF1ZXN0XFxidWlsZFxcbGVnYWN5XFxjbGFzc2VzXFxDbGllbnRFcnJvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgQ2xpZW50RXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgcmVzcG9uc2U7XG4gICAgcmVxdWVzdDtcbiAgICBjb25zdHJ1Y3RvcihyZXNwb25zZSwgcmVxdWVzdCkge1xuICAgICAgICBjb25zdCBtZXNzYWdlID0gYCR7Q2xpZW50RXJyb3IuZXh0cmFjdE1lc3NhZ2UocmVzcG9uc2UpfTogJHtKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgICByZXNwb25zZSxcbiAgICAgICAgICAgIHJlcXVlc3QsXG4gICAgICAgIH0pfWA7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICBPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcywgQ2xpZW50RXJyb3IucHJvdG90eXBlKTtcbiAgICAgICAgdGhpcy5yZXNwb25zZSA9IHJlc3BvbnNlO1xuICAgICAgICB0aGlzLnJlcXVlc3QgPSByZXF1ZXN0O1xuICAgICAgICAvLyB0aGlzIGlzIG5lZWRlZCBhcyBTYWZhcmkgZG9lc24ndCBzdXBwb3J0IC5jYXB0dXJlU3RhY2tUcmFjZVxuICAgICAgICBpZiAodHlwZW9mIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlID09PSBgZnVuY3Rpb25gKSB7XG4gICAgICAgICAgICBFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSh0aGlzLCBDbGllbnRFcnJvcik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgc3RhdGljIGV4dHJhY3RNZXNzYWdlKHJlc3BvbnNlKSB7XG4gICAgICAgIHJldHVybiByZXNwb25zZS5lcnJvcnM/LlswXT8ubWVzc2FnZSA/PyBgR3JhcGhRTCBFcnJvciAoQ29kZTogJHtTdHJpbmcocmVzcG9uc2Uuc3RhdHVzKX0pYDtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1DbGllbnRFcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/classes/ClientError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js":
/*!****************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLClient: () => (/* binding */ GraphQLClient)\n/* harmony export */ });\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(rsc)/./node_modules/graphql-request/build/lib/prelude.js\");\n/* harmony import */ var _functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../functions/batchRequests.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js\");\n/* harmony import */ var _functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../functions/rawRequest.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js\");\n/* harmony import */ var _functions_request_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../functions/request.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/functions/request.js\");\n/* harmony import */ var _helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../helpers/analyzeDocument.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js\");\n/* harmony import */ var _helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../helpers/runRequest.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/helpers/runRequest.js\");\n\n\n\n\n\n\n/**\n * GraphQL Client.\n */\nclass GraphQLClient {\n    url;\n    requestConfig;\n    constructor(url, requestConfig = {}) {\n        this.url = url;\n        this.requestConfig = requestConfig;\n    }\n    /**\n     * Send a GraphQL query to the server.\n     */\n    rawRequest = async (...args) => {\n        const [queryOrOptions, variables, requestHeaders] = args;\n        const rawRequestOptions = (0,_functions_rawRequest_js__WEBPACK_IMPORTED_MODULE_2__.parseRawRequestArgs)(queryOrOptions, variables, requestHeaders);\n        const { headers, fetch = globalThis.fetch, method = `POST`, requestMiddleware, responseMiddleware, excludeOperationName, ...fetchOptions } = this.requestConfig;\n        const { url } = this;\n        if (rawRequestOptions.signal !== undefined) {\n            fetchOptions.signal = rawRequestOptions.signal;\n        }\n        const document = (0,_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__.analyzeDocument)(rawRequestOptions.query, excludeOperationName);\n        const response = await (0,_helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__.runRequest)({\n            url,\n            request: {\n                _tag: `Single`,\n                document,\n                variables: rawRequestOptions.variables,\n            },\n            headers: {\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.callOrIdentity)(headers)),\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)(rawRequestOptions.requestHeaders),\n            },\n            fetch,\n            method,\n            fetchOptions,\n            middleware: requestMiddleware,\n        });\n        if (responseMiddleware) {\n            await responseMiddleware(response, {\n                operationName: document.operationName,\n                variables,\n                url: this.url,\n            });\n        }\n        if (response instanceof Error) {\n            throw response;\n        }\n        return response;\n    };\n    async request(documentOrOptions, ...variablesAndRequestHeaders) {\n        const [variables, requestHeaders] = variablesAndRequestHeaders;\n        const requestOptions = (0,_functions_request_js__WEBPACK_IMPORTED_MODULE_3__.parseRequestArgs)(documentOrOptions, variables, requestHeaders);\n        const { headers, fetch = globalThis.fetch, method = `POST`, requestMiddleware, responseMiddleware, excludeOperationName, ...fetchOptions } = this.requestConfig;\n        const { url } = this;\n        if (requestOptions.signal !== undefined) {\n            fetchOptions.signal = requestOptions.signal;\n        }\n        const analyzedDocument = (0,_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__.analyzeDocument)(requestOptions.document, excludeOperationName);\n        const response = await (0,_helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__.runRequest)({\n            url,\n            request: {\n                _tag: `Single`,\n                document: analyzedDocument,\n                variables: requestOptions.variables,\n            },\n            headers: {\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.callOrIdentity)(headers)),\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)(requestOptions.requestHeaders),\n            },\n            fetch,\n            method,\n            fetchOptions,\n            middleware: requestMiddleware,\n        });\n        if (responseMiddleware) {\n            await responseMiddleware(response, {\n                operationName: analyzedDocument.operationName,\n                variables: requestOptions.variables,\n                url: this.url,\n            });\n        }\n        if (response instanceof Error) {\n            throw response;\n        }\n        return response.data;\n    }\n    async batchRequests(documentsOrOptions, requestHeaders) {\n        const batchRequestOptions = (0,_functions_batchRequests_js__WEBPACK_IMPORTED_MODULE_1__.parseBatchRequestArgs)(documentsOrOptions, requestHeaders);\n        const { headers, excludeOperationName, ...fetchOptions } = this.requestConfig;\n        if (batchRequestOptions.signal !== undefined) {\n            fetchOptions.signal = batchRequestOptions.signal;\n        }\n        const analyzedDocuments = batchRequestOptions.documents.map(({ document }) => (0,_helpers_analyzeDocument_js__WEBPACK_IMPORTED_MODULE_4__.analyzeDocument)(document, excludeOperationName));\n        const expressions = analyzedDocuments.map(({ expression }) => expression);\n        const hasMutations = analyzedDocuments.some(({ isMutation }) => isMutation);\n        const variables = batchRequestOptions.documents.map(({ variables }) => variables);\n        const response = await (0,_helpers_runRequest_js__WEBPACK_IMPORTED_MODULE_5__.runRequest)({\n            url: this.url,\n            request: {\n                _tag: `Batch`,\n                operationName: undefined,\n                query: expressions,\n                hasMutations,\n                variables,\n            },\n            headers: {\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.callOrIdentity)(headers)),\n                ...(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.HeadersInitToPlainObject)(batchRequestOptions.requestHeaders),\n            },\n            fetch: this.requestConfig.fetch ?? globalThis.fetch,\n            method: this.requestConfig.method || `POST`,\n            fetchOptions,\n            middleware: this.requestConfig.requestMiddleware,\n        });\n        if (this.requestConfig.responseMiddleware) {\n            await this.requestConfig.responseMiddleware(response, {\n                operationName: undefined,\n                variables,\n                url: this.url,\n            });\n        }\n        if (response instanceof Error) {\n            throw response;\n        }\n        return response.data;\n    }\n    setHeaders(headers) {\n        this.requestConfig.headers = headers;\n        return this;\n    }\n    /**\n     * Attach a header to the client. All subsequent requests will have this header.\n     */\n    setHeader(key, value) {\n        const { headers } = this.requestConfig;\n        if (headers) {\n            // todo what if headers is in nested array form... ?\n            // @ts-expect-error todo\n            headers[key] = value;\n        }\n        else {\n            this.requestConfig.headers = { [key]: value };\n        }\n        return this;\n    }\n    /**\n     * Change the client endpoint. All subsequent requests will send to this endpoint.\n     */\n    setEndpoint(value) {\n        this.url = value;\n        return this;\n    }\n}\n//# sourceMappingURL=GraphQLClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js":
/*!******************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/batchRequests.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   batchRequests: () => (/* binding */ batchRequests),\n/* harmony export */   parseBatchRequestArgs: () => (/* binding */ parseBatchRequestArgs),\n/* harmony export */   parseBatchRequestsArgsExtended: () => (/* binding */ parseBatchRequestsArgsExtended)\n/* harmony export */ });\n/* harmony import */ var _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../classes/GraphQLClient.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n\n/**\n * Send a batch of GraphQL Document to the GraphQL server for execution.\n *\n * @example\n *\n * ```ts\n * // You can pass a raw string\n *\n * await batchRequests('https://foo.bar/graphql', [\n * {\n *  query: `\n *   {\n *     query {\n *       users\n *     }\n *   }`\n * },\n * {\n *   query: `\n *   {\n *     query {\n *       users\n *     }\n *   }`\n * }])\n *\n * // You can also pass a GraphQL DocumentNode as query. Convenient if you\n * // are using graphql-tag package.\n *\n * import gql from 'graphql-tag'\n *\n * await batchRequests('https://foo.bar/graphql', [{ query: gql`...` }])\n * ```\n */\nconst batchRequests = async (...args) => {\n    const params = parseBatchRequestsArgsExtended(args);\n    const client = new _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(params.url);\n    return client.batchRequests(params);\n};\nconst parseBatchRequestsArgsExtended = (args) => {\n    if (args.length === 1) {\n        return args[0];\n    }\n    else {\n        return {\n            url: args[0],\n            documents: args[1],\n            requestHeaders: args[2],\n            signal: undefined,\n        };\n    }\n};\nconst parseBatchRequestArgs = (documentsOrOptions, requestHeaders) => {\n    // eslint-disable-next-line\n    return documentsOrOptions.documents\n        ? documentsOrOptions\n        : {\n            documents: documentsOrOptions,\n            requestHeaders: requestHeaders,\n            signal: undefined,\n        };\n};\n//# sourceMappingURL=batchRequests.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/functions/batchRequests.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/functions/gql.js":
/*!********************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/gql.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gql: () => (/* binding */ gql)\n/* harmony export */ });\n/**\n * Convenience passthrough template tag to get the benefits of tooling for the gql template tag. This does not actually parse the input into a GraphQL DocumentNode like graphql-tag package does. It just returns the string with any variables given interpolated. Can save you a bit of performance and having to install another package.\n *\n * @example\n * ```\n * import { gql } from 'graphql-request'\n *\n * await request('https://foo.bar/graphql', gql`...`)\n * ```\n *\n * @remarks\n *\n * Several tools in the Node GraphQL ecosystem are hardcoded to specially treat any template tag named \"gql\". For example see this prettier issue: https://github.com/prettier/prettier/issues/4360. Using this template tag has no runtime effect beyond variable interpolation.\n */\nconst gql = (chunks, ...variables) => {\n    return chunks.reduce((acc, chunk, index) => `${acc}${chunk}${index in variables ? String(variables[index]) : ``}`, ``);\n};\n//# sourceMappingURL=gql.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC1yZXF1ZXN0L2J1aWxkL2xlZ2FjeS9mdW5jdGlvbnMvZ3FsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxNQUFNO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLG1EQUFtRCxJQUFJLEVBQUUsTUFBTSxFQUFFLG1EQUFtRDtBQUNwSDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNhbmpheU1cXERlc2t0b3BcXEhBUlNIICAoYnRlY2ggY3NlKVxcaW5yZWFsXFxzdHVkZW50YWktbGFuZGluZ1xcbm9kZV9tb2R1bGVzXFxncmFwaHFsLXJlcXVlc3RcXGJ1aWxkXFxsZWdhY3lcXGZ1bmN0aW9uc1xcZ3FsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29udmVuaWVuY2UgcGFzc3Rocm91Z2ggdGVtcGxhdGUgdGFnIHRvIGdldCB0aGUgYmVuZWZpdHMgb2YgdG9vbGluZyBmb3IgdGhlIGdxbCB0ZW1wbGF0ZSB0YWcuIFRoaXMgZG9lcyBub3QgYWN0dWFsbHkgcGFyc2UgdGhlIGlucHV0IGludG8gYSBHcmFwaFFMIERvY3VtZW50Tm9kZSBsaWtlIGdyYXBocWwtdGFnIHBhY2thZ2UgZG9lcy4gSXQganVzdCByZXR1cm5zIHRoZSBzdHJpbmcgd2l0aCBhbnkgdmFyaWFibGVzIGdpdmVuIGludGVycG9sYXRlZC4gQ2FuIHNhdmUgeW91IGEgYml0IG9mIHBlcmZvcm1hbmNlIGFuZCBoYXZpbmcgdG8gaW5zdGFsbCBhbm90aGVyIHBhY2thZ2UuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYFxuICogaW1wb3J0IHsgZ3FsIH0gZnJvbSAnZ3JhcGhxbC1yZXF1ZXN0J1xuICpcbiAqIGF3YWl0IHJlcXVlc3QoJ2h0dHBzOi8vZm9vLmJhci9ncmFwaHFsJywgZ3FsYC4uLmApXG4gKiBgYGBcbiAqXG4gKiBAcmVtYXJrc1xuICpcbiAqIFNldmVyYWwgdG9vbHMgaW4gdGhlIE5vZGUgR3JhcGhRTCBlY29zeXN0ZW0gYXJlIGhhcmRjb2RlZCB0byBzcGVjaWFsbHkgdHJlYXQgYW55IHRlbXBsYXRlIHRhZyBuYW1lZCBcImdxbFwiLiBGb3IgZXhhbXBsZSBzZWUgdGhpcyBwcmV0dGllciBpc3N1ZTogaHR0cHM6Ly9naXRodWIuY29tL3ByZXR0aWVyL3ByZXR0aWVyL2lzc3Vlcy80MzYwLiBVc2luZyB0aGlzIHRlbXBsYXRlIHRhZyBoYXMgbm8gcnVudGltZSBlZmZlY3QgYmV5b25kIHZhcmlhYmxlIGludGVycG9sYXRpb24uXG4gKi9cbmV4cG9ydCBjb25zdCBncWwgPSAoY2h1bmtzLCAuLi52YXJpYWJsZXMpID0+IHtcbiAgICByZXR1cm4gY2h1bmtzLnJlZHVjZSgoYWNjLCBjaHVuaywgaW5kZXgpID0+IGAke2FjY30ke2NodW5rfSR7aW5kZXggaW4gdmFyaWFibGVzID8gU3RyaW5nKHZhcmlhYmxlc1tpbmRleF0pIDogYGB9YCwgYGApO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdxbC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/functions/gql.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js":
/*!***************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/rawRequest.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseRawRequestArgs: () => (/* binding */ parseRawRequestArgs),\n/* harmony export */   parseRawRequestExtendedArgs: () => (/* binding */ parseRawRequestExtendedArgs),\n/* harmony export */   rawRequest: () => (/* binding */ rawRequest)\n/* harmony export */ });\n/* harmony import */ var _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../classes/GraphQLClient.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n\n/**\n * Send a GraphQL Query to the GraphQL server for execution.\n */\nconst rawRequest = async (...args) => {\n    const [urlOrOptions, query, ...variablesAndRequestHeaders] = args;\n    const requestOptions = parseRawRequestExtendedArgs(urlOrOptions, query, ...variablesAndRequestHeaders);\n    const client = new _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(requestOptions.url);\n    return client.rawRequest({\n        ...requestOptions,\n    });\n};\nconst parseRawRequestExtendedArgs = (urlOrOptions, query, ...variablesAndRequestHeaders) => {\n    const [variables, requestHeaders] = variablesAndRequestHeaders;\n    return typeof urlOrOptions === `string`\n        ? {\n            url: urlOrOptions,\n            query: query,\n            variables,\n            requestHeaders,\n            signal: undefined,\n        }\n        : urlOrOptions;\n};\nconst parseRawRequestArgs = (queryOrOptions, variables, requestHeaders) => {\n    return queryOrOptions.query\n        ? queryOrOptions\n        : {\n            query: queryOrOptions,\n            variables: variables,\n            requestHeaders: requestHeaders,\n            signal: undefined,\n        };\n};\n//# sourceMappingURL=rawRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/functions/rawRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/functions/request.js":
/*!************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/functions/request.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseRequestArgs: () => (/* binding */ parseRequestArgs),\n/* harmony export */   parseRequestExtendedArgs: () => (/* binding */ parseRequestExtendedArgs),\n/* harmony export */   request: () => (/* binding */ request)\n/* harmony export */ });\n/* harmony import */ var _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../classes/GraphQLClient.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/classes/GraphQLClient.js\");\n\n// dprint-ignore\n// eslint-disable-next-line\nasync function request(urlOrOptions, document, ...variablesAndRequestHeaders) {\n    const requestOptions = parseRequestExtendedArgs(urlOrOptions, document, ...variablesAndRequestHeaders);\n    const client = new _classes_GraphQLClient_js__WEBPACK_IMPORTED_MODULE_0__.GraphQLClient(requestOptions.url);\n    return client.request({\n        ...requestOptions,\n    });\n}\nconst parseRequestArgs = (documentOrOptions, variables, requestHeaders) => {\n    return documentOrOptions.document\n        ? documentOrOptions\n        : {\n            document: documentOrOptions,\n            variables: variables,\n            requestHeaders: requestHeaders,\n            signal: undefined,\n        };\n};\nconst parseRequestExtendedArgs = (urlOrOptions, document, ...variablesAndRequestHeaders) => {\n    const [variables, requestHeaders] = variablesAndRequestHeaders;\n    return typeof urlOrOptions === `string`\n        ? {\n            url: urlOrOptions,\n            document: document,\n            variables,\n            requestHeaders,\n            signal: undefined,\n        }\n        : urlOrOptions;\n};\n//# sourceMappingURL=request.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/functions/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js":
/*!******************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeDocument: () => (/* binding */ analyzeDocument)\n/* harmony export */ });\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(rsc)/./node_modules/graphql-request/build/lib/prelude.js\");\n/* harmony import */ var _lib_graphql_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/graphql.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/lib/graphql.js\");\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/language/parser.mjs\");\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/language/printer.mjs\");\n\n\n\n\n/**\n * helpers\n */\nconst extractOperationName = (document) => {\n    let operationName = undefined;\n    const defs = document.definitions.filter(_lib_graphql_js__WEBPACK_IMPORTED_MODULE_1__.isOperationDefinitionNode);\n    if (defs.length === 1) {\n        operationName = defs[0].name?.value;\n    }\n    return operationName;\n};\nconst extractIsMutation = (document) => {\n    let isMutation = false;\n    const defs = document.definitions.filter(_lib_graphql_js__WEBPACK_IMPORTED_MODULE_1__.isOperationDefinitionNode);\n    if (defs.length === 1) {\n        /* eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison --\n         * graphql@15's `OperationTypeNode` is a type, but graphql@16's `OperationTypeNode` is a native TypeScript enum\n         * Therefore, we cannot use `OperationTypeNode.MUTATION` here because it wouldn't work with graphql@15\n         **/\n        isMutation = defs[0].operation === `mutation`;\n    }\n    return isMutation;\n};\nconst analyzeDocument = (document, excludeOperationName) => {\n    const expression = typeof document === `string` ? document : (0,graphql__WEBPACK_IMPORTED_MODULE_2__.print)(document);\n    let isMutation = false;\n    let operationName = undefined;\n    if (excludeOperationName) {\n        return { expression, isMutation, operationName };\n    }\n    const docNode = (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_0__.tryCatch)(() => (typeof document === `string` ? (0,graphql__WEBPACK_IMPORTED_MODULE_3__.parse)(document) : document));\n    if (docNode instanceof Error) {\n        return { expression, isMutation, operationName };\n    }\n    operationName = extractOperationName(docNode);\n    isMutation = extractIsMutation(docNode);\n    return { expression, operationName, isMutation };\n};\n//# sourceMappingURL=analyzeDocument.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/helpers/analyzeDocument.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js":
/*!************************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultJsonSerializer: () => (/* binding */ defaultJsonSerializer)\n/* harmony export */ });\nconst defaultJsonSerializer = JSON;\n//# sourceMappingURL=defaultJsonSerializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC1yZXF1ZXN0L2J1aWxkL2xlZ2FjeS9oZWxwZXJzL2RlZmF1bHRKc29uU2VyaWFsaXplci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTYW5qYXlNXFxEZXNrdG9wXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXGlucmVhbFxcc3R1ZGVudGFpLWxhbmRpbmdcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbC1yZXF1ZXN0XFxidWlsZFxcbGVnYWN5XFxoZWxwZXJzXFxkZWZhdWx0SnNvblNlcmlhbGl6ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGRlZmF1bHRKc29uU2VyaWFsaXplciA9IEpTT047XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZWZhdWx0SnNvblNlcmlhbGl6ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/helpers/runRequest.js":
/*!*************************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/helpers/runRequest.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   runRequest: () => (/* binding */ runRequest)\n/* harmony export */ });\n/* harmony import */ var _lib_http_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/http.js */ \"(rsc)/./node_modules/graphql-request/build/lib/http.js\");\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(rsc)/./node_modules/graphql-request/build/lib/prelude.js\");\n/* harmony import */ var _classes_ClientError_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../classes/ClientError.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/classes/ClientError.js\");\n/* harmony import */ var _lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/graphql.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/lib/graphql.js\");\n/* harmony import */ var _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./defaultJsonSerializer.js */ \"(rsc)/./node_modules/graphql-request/build/legacy/helpers/defaultJsonSerializer.js\");\n\n\n\n\n\n// @ts-expect-error todo\nconst runRequest = async (input) => {\n    // todo make a Config type\n    const config = {\n        ...input,\n        method: input.request._tag === `Single`\n            ? input.request.document.isMutation\n                ? `POST`\n                : (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.uppercase)(input.method ?? `post`)\n            : input.request.hasMutations\n                ? `POST`\n                : (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.uppercase)(input.method ?? `post`),\n        fetchOptions: {\n            ...input.fetchOptions,\n            errorPolicy: input.fetchOptions.errorPolicy ?? `none`,\n        },\n    };\n    const fetcher = createFetcher(config.method);\n    const fetchResponse = await fetcher(config);\n    if (!fetchResponse.ok) {\n        return new _classes_ClientError_js__WEBPACK_IMPORTED_MODULE_2__.ClientError({ status: fetchResponse.status, headers: fetchResponse.headers }, {\n            query: input.request._tag === `Single` ? input.request.document.expression : input.request.query,\n            variables: input.request.variables,\n        });\n    }\n    const result = await parseResultFromResponse(fetchResponse, input.fetchOptions.jsonSerializer ?? _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__.defaultJsonSerializer);\n    if (result instanceof Error)\n        throw result; // todo something better\n    const clientResponseBase = {\n        status: fetchResponse.status,\n        headers: fetchResponse.headers,\n    };\n    if ((0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.isRequestResultHaveErrors)(result) && config.fetchOptions.errorPolicy === `none`) {\n        // todo this client response on error is not consistent with the data type for success\n        const clientResponse = result._tag === `Batch`\n            ? { ...result.executionResults, ...clientResponseBase }\n            : {\n                ...result.executionResult,\n                ...clientResponseBase,\n            };\n        // @ts-expect-error todo\n        return new _classes_ClientError_js__WEBPACK_IMPORTED_MODULE_2__.ClientError(clientResponse, {\n            query: input.request._tag === `Single` ? input.request.document.expression : input.request.query,\n            variables: input.request.variables,\n        });\n    }\n    switch (result._tag) {\n        case `Single`:\n            // @ts-expect-error todo\n            return {\n                ...clientResponseBase,\n                ...executionResultClientResponseFields(config)(result.executionResult),\n            };\n        case `Batch`:\n            return {\n                ...clientResponseBase,\n                data: result.executionResults.map(executionResultClientResponseFields(config)),\n            };\n        default:\n            (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.casesExhausted)(result);\n    }\n};\nconst executionResultClientResponseFields = ($params) => (executionResult) => {\n    return {\n        extensions: executionResult.extensions,\n        data: executionResult.data,\n        errors: $params.fetchOptions.errorPolicy === `all` ? executionResult.errors : undefined,\n    };\n};\nconst parseResultFromResponse = async (response, jsonSerializer) => {\n    const contentType = response.headers.get(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_HEADER);\n    const text = await response.text();\n    if (contentType && (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.isGraphQLContentType)(contentType)) {\n        return (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.parseGraphQLExecutionResult)(jsonSerializer.parse(text));\n    }\n    else {\n        // todo what is this good for...? Seems very random/undefined\n        return (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.parseGraphQLExecutionResult)(text);\n    }\n};\nconst createFetcher = (method) => async (params) => {\n    const headers = new Headers(params.headers);\n    let searchParams = null;\n    let body = undefined;\n    if (!headers.has(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.ACCEPT_HEADER)) {\n        headers.set(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.ACCEPT_HEADER, [_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_GQL, _lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_JSON].join(`, `));\n    }\n    if (method === `POST`) {\n        const $jsonSerializer = params.fetchOptions.jsonSerializer ?? _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__.defaultJsonSerializer;\n        body = $jsonSerializer.stringify(buildBody(params));\n        if (typeof body === `string` && !headers.has(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_HEADER)) {\n            headers.set(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_HEADER, _lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_JSON);\n        }\n    }\n    else {\n        searchParams = buildQueryParams(params);\n    }\n    const init = { method, headers, body, ...params.fetchOptions };\n    let url = new URL(params.url);\n    let initResolved = init;\n    if (params.middleware) {\n        const result = await Promise.resolve(params.middleware({\n            ...init,\n            url: params.url,\n            operationName: params.request._tag === `Single` ? params.request.document.operationName : undefined,\n            variables: params.request.variables,\n        }));\n        const { url: urlNew, ...initNew } = result;\n        url = new URL(urlNew);\n        initResolved = initNew;\n    }\n    if (searchParams) {\n        searchParams.forEach((value, name) => {\n            url.searchParams.append(name, value);\n        });\n    }\n    const $fetch = params.fetch ?? fetch;\n    return await $fetch(url, initResolved);\n};\nconst buildBody = (params) => {\n    switch (params.request._tag) {\n        case `Single`:\n            return {\n                query: params.request.document.expression,\n                variables: params.request.variables,\n                operationName: params.request.document.operationName,\n            };\n        case `Batch`:\n            return (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.zip)(params.request.query, params.request.variables ?? []).map(([query, variables]) => ({\n                query,\n                variables,\n            }));\n        default:\n            throw (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.casesExhausted)(params.request);\n    }\n};\nconst buildQueryParams = (params) => {\n    const $jsonSerializer = params.fetchOptions.jsonSerializer ?? _defaultJsonSerializer_js__WEBPACK_IMPORTED_MODULE_4__.defaultJsonSerializer;\n    const searchParams = new URLSearchParams();\n    switch (params.request._tag) {\n        case `Single`: {\n            searchParams.append(`query`, (0,_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.cleanQuery)(params.request.document.expression));\n            if (params.request.variables) {\n                searchParams.append(`variables`, $jsonSerializer.stringify(params.request.variables));\n            }\n            if (params.request.document.operationName) {\n                searchParams.append(`operationName`, params.request.document.operationName);\n            }\n            return searchParams;\n        }\n        case `Batch`: {\n            const variablesSerialized = params.request.variables?.map((v) => $jsonSerializer.stringify(v)) ?? [];\n            const queriesCleaned = params.request.query.map(_lib_graphql_js__WEBPACK_IMPORTED_MODULE_3__.cleanQuery);\n            const payload = (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.zip)(queriesCleaned, variablesSerialized).map(([query, variables]) => ({\n                query,\n                variables,\n            }));\n            searchParams.append(`query`, $jsonSerializer.stringify(payload));\n            return searchParams;\n        }\n        default:\n            throw (0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.casesExhausted)(params.request);\n    }\n};\n//# sourceMappingURL=runRequest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/helpers/runRequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/legacy/lib/graphql.js":
/*!******************************************************************!*\
  !*** ./node_modules/graphql-request/build/legacy/lib/graphql.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanQuery: () => (/* binding */ cleanQuery),\n/* harmony export */   isExecutionResultHaveErrors: () => (/* binding */ isExecutionResultHaveErrors),\n/* harmony export */   isGraphQLContentType: () => (/* binding */ isGraphQLContentType),\n/* harmony export */   isOperationDefinitionNode: () => (/* binding */ isOperationDefinitionNode),\n/* harmony export */   isRequestResultHaveErrors: () => (/* binding */ isRequestResultHaveErrors),\n/* harmony export */   parseExecutionResult: () => (/* binding */ parseExecutionResult),\n/* harmony export */   parseGraphQLExecutionResult: () => (/* binding */ parseGraphQLExecutionResult)\n/* harmony export */ });\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql */ \"(rsc)/./node_modules/graphql/language/kinds.mjs\");\n/* harmony import */ var _lib_http_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/http.js */ \"(rsc)/./node_modules/graphql-request/build/lib/http.js\");\n/* harmony import */ var _lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/prelude.js */ \"(rsc)/./node_modules/graphql-request/build/lib/prelude.js\");\n\n\n\n/**\n * Clean a GraphQL document to send it via a GET query\n */\nconst cleanQuery = (str) => str.replace(/([\\s,]|#[^\\n\\r]+)+/g, ` `).trim();\nconst isGraphQLContentType = (contentType) => {\n    const contentTypeLower = contentType.toLowerCase();\n    return contentTypeLower.includes(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_GQL) || contentTypeLower.includes(_lib_http_js__WEBPACK_IMPORTED_MODULE_0__.CONTENT_TYPE_JSON);\n};\nconst parseGraphQLExecutionResult = (result) => {\n    try {\n        if (Array.isArray(result)) {\n            return {\n                _tag: `Batch`,\n                executionResults: result.map(parseExecutionResult),\n            };\n        }\n        else if ((0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result)) {\n            return {\n                _tag: `Single`,\n                executionResult: parseExecutionResult(result),\n            };\n        }\n        else {\n            throw new Error(`Invalid execution result: result is not object or array. \\nGot:\\n${String(result)}`);\n        }\n    }\n    catch (e) {\n        return e;\n    }\n};\n/**\n * Example result:\n *\n * ```\n * {\n *  \"data\": null,\n *  \"errors\": [{\n *    \"message\": \"custom error\",\n *    \"locations\": [{ \"line\": 2, \"column\": 3 }],\n *    \"path\": [\"playerNew\"]\n *  }]\n * }\n * ```\n */\nconst parseExecutionResult = (result) => {\n    if (typeof result !== `object` || result === null) {\n        throw new Error(`Invalid execution result: result is not object`);\n    }\n    let errors = undefined;\n    let data = undefined;\n    let extensions = undefined;\n    if (`errors` in result) {\n        if (!(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result.errors) && !Array.isArray(result.errors)) {\n            throw new Error(`Invalid execution result: errors is not plain object OR array`); // prettier-ignore\n        }\n        errors = result.errors;\n    }\n    // todo add test coverage for case of null. @see https://github.com/jasonkuhrt/graphql-request/issues/739\n    if (`data` in result) {\n        if (!(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result.data) && result.data !== null) {\n            throw new Error(`Invalid execution result: data is not plain object`); // prettier-ignore\n        }\n        data = result.data;\n    }\n    if (`extensions` in result) {\n        if (!(0,_lib_prelude_js__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(result.extensions))\n            throw new Error(`Invalid execution result: extensions is not plain object`); // prettier-ignore\n        extensions = result.extensions;\n    }\n    return {\n        data,\n        errors,\n        extensions,\n    };\n};\nconst isRequestResultHaveErrors = (result) => result._tag === `Batch`\n    ? result.executionResults.some(isExecutionResultHaveErrors)\n    : isExecutionResultHaveErrors(result.executionResult);\nconst isExecutionResultHaveErrors = (result) => Array.isArray(result.errors) ? result.errors.length > 0 : Boolean(result.errors);\nconst isOperationDefinitionNode = (definition) => {\n    return (typeof definition === `object`\n        && definition !== null\n        && `kind` in definition\n        && definition.kind === graphql__WEBPACK_IMPORTED_MODULE_2__.Kind.OPERATION_DEFINITION);\n};\n//# sourceMappingURL=graphql.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/legacy/lib/graphql.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/lib/http.js":
/*!********************************************************!*\
  !*** ./node_modules/graphql-request/build/lib/http.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACCEPT_HEADER: () => (/* binding */ ACCEPT_HEADER),\n/* harmony export */   CONTENT_TYPE_GQL: () => (/* binding */ CONTENT_TYPE_GQL),\n/* harmony export */   CONTENT_TYPE_HEADER: () => (/* binding */ CONTENT_TYPE_HEADER),\n/* harmony export */   CONTENT_TYPE_JSON: () => (/* binding */ CONTENT_TYPE_JSON),\n/* harmony export */   statusCodes: () => (/* binding */ statusCodes)\n/* harmony export */ });\nconst ACCEPT_HEADER = `Accept`;\nconst CONTENT_TYPE_HEADER = `Content-Type`;\nconst CONTENT_TYPE_JSON = `application/json`;\nconst CONTENT_TYPE_GQL = `application/graphql-response+json`;\nconst statusCodes = {\n    success: 200,\n};\n//# sourceMappingURL=http.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC1yZXF1ZXN0L2J1aWxkL2xpYi9odHRwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTYW5qYXlNXFxEZXNrdG9wXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXGlucmVhbFxcc3R1ZGVudGFpLWxhbmRpbmdcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbC1yZXF1ZXN0XFxidWlsZFxcbGliXFxodHRwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBBQ0NFUFRfSEVBREVSID0gYEFjY2VwdGA7XG5leHBvcnQgY29uc3QgQ09OVEVOVF9UWVBFX0hFQURFUiA9IGBDb250ZW50LVR5cGVgO1xuZXhwb3J0IGNvbnN0IENPTlRFTlRfVFlQRV9KU09OID0gYGFwcGxpY2F0aW9uL2pzb25gO1xuZXhwb3J0IGNvbnN0IENPTlRFTlRfVFlQRV9HUUwgPSBgYXBwbGljYXRpb24vZ3JhcGhxbC1yZXNwb25zZStqc29uYDtcbmV4cG9ydCBjb25zdCBzdGF0dXNDb2RlcyA9IHtcbiAgICBzdWNjZXNzOiAyMDAsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aHR0cC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/lib/http.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql-request/build/lib/prelude.js":
/*!***********************************************************!*\
  !*** ./node_modules/graphql-request/build/lib/prelude.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HeadersInitToPlainObject: () => (/* binding */ HeadersInitToPlainObject),\n/* harmony export */   HeadersInstanceToPlainObject: () => (/* binding */ HeadersInstanceToPlainObject),\n/* harmony export */   assertArray: () => (/* binding */ assertArray),\n/* harmony export */   assertObject: () => (/* binding */ assertObject),\n/* harmony export */   callOrIdentity: () => (/* binding */ callOrIdentity),\n/* harmony export */   capitalizeFirstLetter: () => (/* binding */ capitalizeFirstLetter),\n/* harmony export */   casesExhausted: () => (/* binding */ casesExhausted),\n/* harmony export */   createDeferred: () => (/* binding */ createDeferred),\n/* harmony export */   debug: () => (/* binding */ debug),\n/* harmony export */   debugSub: () => (/* binding */ debugSub),\n/* harmony export */   entries: () => (/* binding */ entries),\n/* harmony export */   errorFromMaybeError: () => (/* binding */ errorFromMaybeError),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isPromiseLikeValue: () => (/* binding */ isPromiseLikeValue),\n/* harmony export */   lowerCaseFirstLetter: () => (/* binding */ lowerCaseFirstLetter),\n/* harmony export */   mapValues: () => (/* binding */ mapValues),\n/* harmony export */   partitionErrors: () => (/* binding */ partitionErrors),\n/* harmony export */   tryCatch: () => (/* binding */ tryCatch),\n/* harmony export */   uppercase: () => (/* binding */ uppercase),\n/* harmony export */   values: () => (/* binding */ values),\n/* harmony export */   zip: () => (/* binding */ zip)\n/* harmony export */ });\nconst uppercase = (str) => str.toUpperCase();\nconst callOrIdentity = (value) => {\n    return typeof value === `function` ? value() : value;\n};\nconst zip = (a, b) => a.map((k, i) => [k, b[i]]);\nconst HeadersInitToPlainObject = (headers) => {\n    let oHeaders = {};\n    if (headers instanceof Headers) {\n        oHeaders = HeadersInstanceToPlainObject(headers);\n    }\n    else if (Array.isArray(headers)) {\n        headers.forEach(([name, value]) => {\n            if (name && value !== undefined) {\n                oHeaders[name] = value;\n            }\n        });\n    }\n    else if (headers) {\n        oHeaders = headers;\n    }\n    return oHeaders;\n};\nconst HeadersInstanceToPlainObject = (headers) => {\n    const o = {};\n    headers.forEach((v, k) => {\n        o[k] = v;\n    });\n    return o;\n};\nconst tryCatch = (fn) => {\n    try {\n        const result = fn();\n        if (isPromiseLikeValue(result)) {\n            return result.catch((error) => {\n                return errorFromMaybeError(error);\n            });\n        }\n        return result;\n    }\n    catch (error) {\n        return errorFromMaybeError(error);\n    }\n};\n/**\n * Ensure that the given value is an error and return it. If it is not an error than\n * wrap it in one, passing the given value as the error message.\n */\nconst errorFromMaybeError = (maybeError) => {\n    if (maybeError instanceof Error)\n        return maybeError;\n    return new Error(String(maybeError));\n};\nconst isPromiseLikeValue = (value) => {\n    return (typeof value === `object`\n        && value !== null\n        && `then` in value\n        && typeof value.then === `function`\n        && `catch` in value\n        && typeof value.catch === `function`\n        && `finally` in value\n        && typeof value.finally === `function`);\n};\nconst casesExhausted = (value) => {\n    throw new Error(`Unhandled case: ${String(value)}`);\n};\nconst isPlainObject = (value) => {\n    return typeof value === `object` && value !== null && !Array.isArray(value);\n};\nconst entries = (obj) => Object.entries(obj);\nconst values = (obj) => Object.values(obj);\nconst mapValues = (object, fn) => {\n    return Object.fromEntries(Object.entries(object).map(([key, value]) => {\n        return [key, fn(value, key)];\n    }));\n};\nconst lowerCaseFirstLetter = (s) => {\n    return s.charAt(0).toLowerCase() + s.slice(1);\n};\nfunction assertArray(v) {\n    if (!Array.isArray(v))\n        throw new Error(`Expected array. Got: ${String(v)}`);\n}\nfunction assertObject(v) {\n    if (v === null || typeof v !== `object`)\n        throw new Error(`Expected object. Got: ${String(v)}`);\n}\nconst capitalizeFirstLetter = (string) => string.charAt(0).toUpperCase() + string.slice(1);\nconst createDeferred = (options) => {\n    let isResolved = false;\n    let resolve;\n    let reject;\n    const promise = new Promise(($resolve, $reject) => {\n        resolve = $resolve;\n        reject = $reject;\n    });\n    return {\n        promise,\n        isResolved: () => isResolved,\n        resolve: (value) => {\n            isResolved = true;\n            if (options?.strict && isResolved) {\n                throw new Error(`Deferred is already resolved. Attempted to resolve with: ${JSON.stringify(value)}`);\n            }\n            resolve(value);\n        },\n        reject: (error) => reject(error),\n    };\n};\nconst debug = (...args) => {\n    if (process.env[`DEBUG`]) {\n        console.log(...args);\n    }\n};\nconst debugSub = (...args) => (...subArgs) => {\n    debug(...args, ...subArgs);\n};\nconst partitionErrors = (array) => {\n    const errors = [];\n    const values = [];\n    for (const item of array) {\n        if (item instanceof Error) {\n            errors.push(item);\n        }\n        else {\n            values.push(item);\n        }\n    }\n    return [values, errors];\n};\n//# sourceMappingURL=prelude.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql-request/build/lib/prelude.js\n");

/***/ })

};
;