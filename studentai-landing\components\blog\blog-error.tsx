"use client"

import { <PERSON><PERSON><PERSON>ir<PERSON>, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface BlogErrorProps {
  title?: string
  message?: string
  onRetry?: () => void
  showRetry?: boolean
}

export function BlogError({ 
  title = "Failed to load content",
  message = "We're having trouble loading the blog content. Please try again later.",
  onRetry,
  showRetry = true
}: BlogErrorProps) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] space-y-6">
      <Alert className="max-w-md">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{message}</AlertDescription>
      </Alert>
      
      {showRetry && onRetry && (
        <Button onClick={onRetry} variant="outline" className="gap-2">
          <RefreshCw className="h-4 w-4" />
          Try Again
        </Button>
      )}
    </div>
  )
}

export function BlogPostNotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] space-y-6 text-center">
      <div className="space-y-2">
        <h1 className="text-4xl font-bold">404</h1>
        <h2 className="text-2xl font-semibold">Blog Post Not Found</h2>
        <p className="text-muted-foreground max-w-md">
          The blog post you're looking for doesn't exist or has been moved.
        </p>
      </div>
      
      <Button asChild>
        <a href="/blog">Back to Blog</a>
      </Button>
    </div>
  )
}

export function BlogConnectionError() {
  return (
    <BlogError
      title="Connection Error"
      message="Unable to connect to the content management system. Using cached content where available."
      showRetry={false}
    />
  )
}
