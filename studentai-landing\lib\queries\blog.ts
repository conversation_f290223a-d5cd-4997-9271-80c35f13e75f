// Fragment for Asset fields
export const ASSET_FRAGMENT = `
  fragment AssetFragment on Asset {
    id
    url
    fileName
    mimeType
    size
    width
    height
    alt
  }
`

// Fragment for Author fields
export const AUTHOR_FRAGMENT = `
  fragment AuthorFragment on Author {
    id
    name
    title
    bio {
      html
      text
      markdown
    }
    avatar {
      ...AssetFragment
    }
    email
    socialLinks
    createdAt
    updatedAt
  }
`

// Fragment for Category fields
export const CATEGORY_FRAGMENT = `
  fragment CategoryFragment on Category {
    id
    name
    slug
    description
    color
    createdAt
    updatedAt
  }
`

// Fragment for Tag fields
export const TAG_FRAGMENT = `
  fragment TagFragment on Tag {
    id
    name
    slug
    createdAt
    updatedAt
  }
`

// Fragment for Blog Post fields
export const BLOG_POST_FRAGMENT = `
  fragment BlogPostFragment on BlogPost {
    id
    title
    slug
    excerpt
    content {
      html
      text
      markdown
    }
    coverImage {
      ...AssetFragment
    }
    author {
      ...AuthorFragment
    }
    category {
      ...CategoryFragment
    }
    tags {
      ...TagFragment
    }
    featured
    published
    publishedAt
    seoTitle
    seoDescription
    readingTime
    createdAt
    updatedAt
  }
`

// Query to get all blog posts
export const GET_BLOG_POSTS = `
  ${ASSET_FRAGMENT}
  ${AUTHOR_FRAGMENT}
  ${CATEGORY_FRAGMENT}
  ${TAG_FRAGMENT}
  ${BLOG_POST_FRAGMENT}
  
  query GetBlogPosts(
    $first: Int = 10
    $skip: Int = 0
    $orderBy: BlogPostOrderByInput = publishedAt_DESC
    $where: BlogPostWhereInput
  ) {
    blogPosts(
      first: $first
      skip: $skip
      orderBy: $orderBy
      where: $where
    ) {
      ...BlogPostFragment
    }
  }
`

// Query to get a single blog post by slug
export const GET_BLOG_POST_BY_SLUG = `
  ${ASSET_FRAGMENT}
  ${AUTHOR_FRAGMENT}
  ${CATEGORY_FRAGMENT}
  ${TAG_FRAGMENT}
  ${BLOG_POST_FRAGMENT}
  
  query GetBlogPostBySlug($slug: String!) {
    blogPost(where: { slug: $slug }) {
      ...BlogPostFragment
    }
  }
`

// Query to get featured blog posts
export const GET_FEATURED_BLOG_POSTS = `
  ${ASSET_FRAGMENT}
  ${AUTHOR_FRAGMENT}
  ${CATEGORY_FRAGMENT}
  ${TAG_FRAGMENT}
  ${BLOG_POST_FRAGMENT}
  
  query GetFeaturedBlogPosts($first: Int = 5) {
    blogPosts(
      first: $first
      where: { featured: true, published: true }
      orderBy: publishedAt_DESC
    ) {
      ...BlogPostFragment
    }
  }
`

// Query to get recent blog posts
export const GET_RECENT_BLOG_POSTS = `
  ${ASSET_FRAGMENT}
  ${AUTHOR_FRAGMENT}
  ${CATEGORY_FRAGMENT}
  ${TAG_FRAGMENT}
  ${BLOG_POST_FRAGMENT}

  query GetRecentBlogPosts($first: Int = 5, $excludeId: ID) {
    blogPosts(
      first: $first
      where: { published: true, id_not: $excludeId }
      orderBy: publishedAt_DESC
    ) {
      ...BlogPostFragment
    }
  }
`

// Query to get blog posts by category
export const GET_BLOG_POSTS_BY_CATEGORY = `
  ${ASSET_FRAGMENT}
  ${AUTHOR_FRAGMENT}
  ${CATEGORY_FRAGMENT}
  ${TAG_FRAGMENT}
  ${BLOG_POST_FRAGMENT}

  query GetBlogPostsByCategory(
    $categorySlug: String!
    $first: Int = 10
    $skip: Int = 0
  ) {
    blogPosts(
      first: $first
      skip: $skip
      where: { published: true, category: { slug: $categorySlug } }
      orderBy: publishedAt_DESC
    ) {
      ...BlogPostFragment
    }
  }
`

// Query to get blog posts by tag
export const GET_BLOG_POSTS_BY_TAG = `
  ${ASSET_FRAGMENT}
  ${AUTHOR_FRAGMENT}
  ${CATEGORY_FRAGMENT}
  ${TAG_FRAGMENT}
  ${BLOG_POST_FRAGMENT}

  query GetBlogPostsByTag(
    $tagSlug: String!
    $first: Int = 10
    $skip: Int = 0
  ) {
    blogPosts(
      first: $first
      skip: $skip
      where: { published: true, tags_some: { slug: $tagSlug } }
      orderBy: publishedAt_DESC
    ) {
      ...BlogPostFragment
    }
  }
`

// Query to get all categories
export const GET_CATEGORIES = `
  ${CATEGORY_FRAGMENT}

  query GetCategories {
    categories(orderBy: name_ASC) {
      ...CategoryFragment
    }
  }
`

// Query to get all tags
export const GET_TAGS = `
  ${TAG_FRAGMENT}

  query GetTags {
    tags(orderBy: name_ASC) {
      ...TagFragment
    }
  }
`

// Query to search blog posts
export const SEARCH_BLOG_POSTS = `
  ${ASSET_FRAGMENT}
  ${AUTHOR_FRAGMENT}
  ${CATEGORY_FRAGMENT}
  ${TAG_FRAGMENT}
  ${BLOG_POST_FRAGMENT}

  query SearchBlogPosts(
    $searchTerm: String!
    $first: Int = 10
    $skip: Int = 0
  ) {
    blogPosts(
      first: $first
      skip: $skip
      where: {
        published: true
        OR: [
          { title_contains: $searchTerm }
          { excerpt_contains: $searchTerm }
          { content_contains: $searchTerm }
        ]
      }
      orderBy: publishedAt_DESC
    ) {
      ...BlogPostFragment
    }
  }
`
