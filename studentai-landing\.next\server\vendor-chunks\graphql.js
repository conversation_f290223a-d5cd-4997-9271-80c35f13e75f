"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/graphql";
exports.ids = ["vendor-chunks/graphql"];
exports.modules = {

/***/ "(rsc)/./node_modules/graphql/error/GraphQLError.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/graphql/error/GraphQLError.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLError: () => (/* binding */ GraphQLError),\n/* harmony export */   formatError: () => (/* binding */ formatError),\n/* harmony export */   printError: () => (/* binding */ printError)\n/* harmony export */ });\n/* harmony import */ var _jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/isObjectLike.mjs */ \"(rsc)/./node_modules/graphql/jsutils/isObjectLike.mjs\");\n/* harmony import */ var _language_location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../language/location.mjs */ \"(rsc)/./node_modules/graphql/language/location.mjs\");\n/* harmony import */ var _language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../language/printLocation.mjs */ \"(rsc)/./node_modules/graphql/language/printLocation.mjs\");\n\n\n\n\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5],\n    };\n  }\n\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nclass GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n\n    const { nodes, source, positions, path, originalError, extensions } =\n      toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError =\n      originalError !== null && originalError !== void 0\n        ? originalError\n        : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(\n      Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined,\n    );\n    const nodeLocations = undefinedIfEmpty(\n      (_this$nodes = this.nodes) === null || _this$nodes === void 0\n        ? void 0\n        : _this$nodes.map((node) => node.loc).filter((loc) => loc != null),\n    ); // Compute locations in the source for the given nodes/positions.\n\n    this.source =\n      source !== null && source !== void 0\n        ? source\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : (_nodeLocations$ = nodeLocations[0]) === null ||\n          _nodeLocations$ === void 0\n        ? void 0\n        : _nodeLocations$.source;\n    this.positions =\n      positions !== null && positions !== void 0\n        ? positions\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => loc.start);\n    this.locations =\n      positions && source\n        ? positions.map((pos) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(source, pos))\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(loc.source, loc.start));\n    const originalExtensions = (0,_jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__.isObjectLike)(\n      originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions,\n    )\n      ? originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions\n      : undefined;\n    this.extensions =\n      (_ref =\n        extensions !== null && extensions !== void 0\n          ? extensions\n          : originalExtensions) !== null && _ref !== void 0\n        ? _ref\n        : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true,\n      },\n      name: {\n        enumerable: false,\n      },\n      nodes: {\n        enumerable: false,\n      },\n      source: {\n        enumerable: false,\n      },\n      positions: {\n        enumerable: false,\n      },\n      originalError: {\n        enumerable: false,\n      },\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (\n      originalError !== null &&\n      originalError !== void 0 &&\n      originalError.stack\n    ) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true,\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true,\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n\n  toString() {\n    let output = this.message;\n\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printLocation)(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printSourceLocation)(this.source, location);\n      }\n    }\n\n    return output;\n  }\n\n  toJSON() {\n    const formattedError = {\n      message: this.message,\n    };\n\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n\n    return formattedError;\n  }\n}\n\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nfunction printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nfunction formatError(error) {\n  return error.toJSON();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/error/GraphQLError.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/error/syntaxError.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/error/syntaxError.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   syntaxError: () => (/* binding */ syntaxError)\n/* harmony export */ });\n/* harmony import */ var _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GraphQLError.mjs */ \"(rsc)/./node_modules/graphql/error/GraphQLError.mjs\");\n\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nfunction syntaxError(source, position, description) {\n  return new _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(`Syntax Error: ${description}`, {\n    source,\n    positions: [position],\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9lcnJvci9zeW50YXhFcnJvci5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0Q7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUCxhQUFhLDJEQUFZLGtCQUFrQixZQUFZO0FBQ3ZEO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcU2FuamF5TVxcRGVza3RvcFxcSEFSU0ggIChidGVjaCBjc2UpXFxpbnJlYWxcXHN0dWRlbnRhaS1sYW5kaW5nXFxub2RlX21vZHVsZXNcXGdyYXBocWxcXGVycm9yXFxzeW50YXhFcnJvci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgR3JhcGhRTEVycm9yIH0gZnJvbSAnLi9HcmFwaFFMRXJyb3IubWpzJztcbi8qKlxuICogUHJvZHVjZXMgYSBHcmFwaFFMRXJyb3IgcmVwcmVzZW50aW5nIGEgc3ludGF4IGVycm9yLCBjb250YWluaW5nIHVzZWZ1bFxuICogZGVzY3JpcHRpdmUgaW5mb3JtYXRpb24gYWJvdXQgdGhlIHN5bnRheCBlcnJvcidzIHBvc2l0aW9uIGluIHRoZSBzb3VyY2UuXG4gKi9cblxuZXhwb3J0IGZ1bmN0aW9uIHN5bnRheEVycm9yKHNvdXJjZSwgcG9zaXRpb24sIGRlc2NyaXB0aW9uKSB7XG4gIHJldHVybiBuZXcgR3JhcGhRTEVycm9yKGBTeW50YXggRXJyb3I6ICR7ZGVzY3JpcHRpb259YCwge1xuICAgIHNvdXJjZSxcbiAgICBwb3NpdGlvbnM6IFtwb3NpdGlvbl0sXG4gIH0pO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/error/syntaxError.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/jsutils/devAssert.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/jsutils/devAssert.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   devAssert: () => (/* binding */ devAssert)\n/* harmony export */ });\nfunction devAssert(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(message);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2RldkFzc2VydC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcU2FuamF5TVxcRGVza3RvcFxcSEFSU0ggIChidGVjaCBjc2UpXFxpbnJlYWxcXHN0dWRlbnRhaS1sYW5kaW5nXFxub2RlX21vZHVsZXNcXGdyYXBocWxcXGpzdXRpbHNcXGRldkFzc2VydC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGRldkFzc2VydChjb25kaXRpb24sIG1lc3NhZ2UpIHtcbiAgY29uc3QgYm9vbGVhbkNvbmRpdGlvbiA9IEJvb2xlYW4oY29uZGl0aW9uKTtcblxuICBpZiAoIWJvb2xlYW5Db25kaXRpb24pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IobWVzc2FnZSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/jsutils/devAssert.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/jsutils/inspect.mjs":
/*!**************************************************!*\
  !*** ./node_modules/graphql/jsutils/inspect.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inspect: () => (/* binding */ inspect)\n/* harmony export */ });\nconst MAX_ARRAY_LENGTH = 10;\nconst MAX_RECURSIVE_DEPTH = 2;\n/**\n * Used to print values in error messages.\n */\n\nfunction inspect(value) {\n  return formatValue(value, []);\n}\n\nfunction formatValue(value, seenValues) {\n  switch (typeof value) {\n    case 'string':\n      return JSON.stringify(value);\n\n    case 'function':\n      return value.name ? `[function ${value.name}]` : '[function]';\n\n    case 'object':\n      return formatObjectValue(value, seenValues);\n\n    default:\n      return String(value);\n  }\n}\n\nfunction formatObjectValue(value, previouslySeenValues) {\n  if (value === null) {\n    return 'null';\n  }\n\n  if (previouslySeenValues.includes(value)) {\n    return '[Circular]';\n  }\n\n  const seenValues = [...previouslySeenValues, value];\n\n  if (isJSONable(value)) {\n    const jsonValue = value.toJSON(); // check for infinite recursion\n\n    if (jsonValue !== value) {\n      return typeof jsonValue === 'string'\n        ? jsonValue\n        : formatValue(jsonValue, seenValues);\n    }\n  } else if (Array.isArray(value)) {\n    return formatArray(value, seenValues);\n  }\n\n  return formatObject(value, seenValues);\n}\n\nfunction isJSONable(value) {\n  return typeof value.toJSON === 'function';\n}\n\nfunction formatObject(object, seenValues) {\n  const entries = Object.entries(object);\n\n  if (entries.length === 0) {\n    return '{}';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[' + getObjectTag(object) + ']';\n  }\n\n  const properties = entries.map(\n    ([key, value]) => key + ': ' + formatValue(value, seenValues),\n  );\n  return '{ ' + properties.join(', ') + ' }';\n}\n\nfunction formatArray(array, seenValues) {\n  if (array.length === 0) {\n    return '[]';\n  }\n\n  if (seenValues.length > MAX_RECURSIVE_DEPTH) {\n    return '[Array]';\n  }\n\n  const len = Math.min(MAX_ARRAY_LENGTH, array.length);\n  const remaining = array.length - len;\n  const items = [];\n\n  for (let i = 0; i < len; ++i) {\n    items.push(formatValue(array[i], seenValues));\n  }\n\n  if (remaining === 1) {\n    items.push('... 1 more item');\n  } else if (remaining > 1) {\n    items.push(`... ${remaining} more items`);\n  }\n\n  return '[' + items.join(', ') + ']';\n}\n\nfunction getObjectTag(object) {\n  const tag = Object.prototype.toString\n    .call(object)\n    .replace(/^\\[object /, '')\n    .replace(/]$/, '');\n\n  if (tag === 'Object' && typeof object.constructor === 'function') {\n    const name = object.constructor.name;\n\n    if (typeof name === 'string' && name !== '') {\n      return name;\n    }\n  }\n\n  return tag;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2luc3BlY3QubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QyxXQUFXOztBQUVsRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLHNDQUFzQzs7QUFFdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjO0FBQ2Q7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVksK0JBQStCO0FBQzNDOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCLFNBQVM7QUFDM0I7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLHNCQUFzQixXQUFXO0FBQ2pDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcU2FuamF5TVxcRGVza3RvcFxcSEFSU0ggIChidGVjaCBjc2UpXFxpbnJlYWxcXHN0dWRlbnRhaS1sYW5kaW5nXFxub2RlX21vZHVsZXNcXGdyYXBocWxcXGpzdXRpbHNcXGluc3BlY3QubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IE1BWF9BUlJBWV9MRU5HVEggPSAxMDtcbmNvbnN0IE1BWF9SRUNVUlNJVkVfREVQVEggPSAyO1xuLyoqXG4gKiBVc2VkIHRvIHByaW50IHZhbHVlcyBpbiBlcnJvciBtZXNzYWdlcy5cbiAqL1xuXG5leHBvcnQgZnVuY3Rpb24gaW5zcGVjdCh2YWx1ZSkge1xuICByZXR1cm4gZm9ybWF0VmFsdWUodmFsdWUsIFtdKTtcbn1cblxuZnVuY3Rpb24gZm9ybWF0VmFsdWUodmFsdWUsIHNlZW5WYWx1ZXMpIHtcbiAgc3dpdGNoICh0eXBlb2YgdmFsdWUpIHtcbiAgICBjYXNlICdzdHJpbmcnOlxuICAgICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KHZhbHVlKTtcblxuICAgIGNhc2UgJ2Z1bmN0aW9uJzpcbiAgICAgIHJldHVybiB2YWx1ZS5uYW1lID8gYFtmdW5jdGlvbiAke3ZhbHVlLm5hbWV9XWAgOiAnW2Z1bmN0aW9uXSc7XG5cbiAgICBjYXNlICdvYmplY3QnOlxuICAgICAgcmV0dXJuIGZvcm1hdE9iamVjdFZhbHVlKHZhbHVlLCBzZWVuVmFsdWVzKTtcblxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gU3RyaW5nKHZhbHVlKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBmb3JtYXRPYmplY3RWYWx1ZSh2YWx1ZSwgcHJldmlvdXNseVNlZW5WYWx1ZXMpIHtcbiAgaWYgKHZhbHVlID09PSBudWxsKSB7XG4gICAgcmV0dXJuICdudWxsJztcbiAgfVxuXG4gIGlmIChwcmV2aW91c2x5U2VlblZhbHVlcy5pbmNsdWRlcyh2YWx1ZSkpIHtcbiAgICByZXR1cm4gJ1tDaXJjdWxhcl0nO1xuICB9XG5cbiAgY29uc3Qgc2VlblZhbHVlcyA9IFsuLi5wcmV2aW91c2x5U2VlblZhbHVlcywgdmFsdWVdO1xuXG4gIGlmIChpc0pTT05hYmxlKHZhbHVlKSkge1xuICAgIGNvbnN0IGpzb25WYWx1ZSA9IHZhbHVlLnRvSlNPTigpOyAvLyBjaGVjayBmb3IgaW5maW5pdGUgcmVjdXJzaW9uXG5cbiAgICBpZiAoanNvblZhbHVlICE9PSB2YWx1ZSkge1xuICAgICAgcmV0dXJuIHR5cGVvZiBqc29uVmFsdWUgPT09ICdzdHJpbmcnXG4gICAgICAgID8ganNvblZhbHVlXG4gICAgICAgIDogZm9ybWF0VmFsdWUoanNvblZhbHVlLCBzZWVuVmFsdWVzKTtcbiAgICB9XG4gIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICByZXR1cm4gZm9ybWF0QXJyYXkodmFsdWUsIHNlZW5WYWx1ZXMpO1xuICB9XG5cbiAgcmV0dXJuIGZvcm1hdE9iamVjdCh2YWx1ZSwgc2VlblZhbHVlcyk7XG59XG5cbmZ1bmN0aW9uIGlzSlNPTmFibGUodmFsdWUpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZS50b0pTT04gPT09ICdmdW5jdGlvbic7XG59XG5cbmZ1bmN0aW9uIGZvcm1hdE9iamVjdChvYmplY3QsIHNlZW5WYWx1ZXMpIHtcbiAgY29uc3QgZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKG9iamVjdCk7XG5cbiAgaWYgKGVudHJpZXMubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuICd7fSc7XG4gIH1cblxuICBpZiAoc2VlblZhbHVlcy5sZW5ndGggPiBNQVhfUkVDVVJTSVZFX0RFUFRIKSB7XG4gICAgcmV0dXJuICdbJyArIGdldE9iamVjdFRhZyhvYmplY3QpICsgJ10nO1xuICB9XG5cbiAgY29uc3QgcHJvcGVydGllcyA9IGVudHJpZXMubWFwKFxuICAgIChba2V5LCB2YWx1ZV0pID0+IGtleSArICc6ICcgKyBmb3JtYXRWYWx1ZSh2YWx1ZSwgc2VlblZhbHVlcyksXG4gICk7XG4gIHJldHVybiAneyAnICsgcHJvcGVydGllcy5qb2luKCcsICcpICsgJyB9Jztcbn1cblxuZnVuY3Rpb24gZm9ybWF0QXJyYXkoYXJyYXksIHNlZW5WYWx1ZXMpIHtcbiAgaWYgKGFycmF5Lmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiAnW10nO1xuICB9XG5cbiAgaWYgKHNlZW5WYWx1ZXMubGVuZ3RoID4gTUFYX1JFQ1VSU0lWRV9ERVBUSCkge1xuICAgIHJldHVybiAnW0FycmF5XSc7XG4gIH1cblxuICBjb25zdCBsZW4gPSBNYXRoLm1pbihNQVhfQVJSQVlfTEVOR1RILCBhcnJheS5sZW5ndGgpO1xuICBjb25zdCByZW1haW5pbmcgPSBhcnJheS5sZW5ndGggLSBsZW47XG4gIGNvbnN0IGl0ZW1zID0gW107XG5cbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW47ICsraSkge1xuICAgIGl0ZW1zLnB1c2goZm9ybWF0VmFsdWUoYXJyYXlbaV0sIHNlZW5WYWx1ZXMpKTtcbiAgfVxuXG4gIGlmIChyZW1haW5pbmcgPT09IDEpIHtcbiAgICBpdGVtcy5wdXNoKCcuLi4gMSBtb3JlIGl0ZW0nKTtcbiAgfSBlbHNlIGlmIChyZW1haW5pbmcgPiAxKSB7XG4gICAgaXRlbXMucHVzaChgLi4uICR7cmVtYWluaW5nfSBtb3JlIGl0ZW1zYCk7XG4gIH1cblxuICByZXR1cm4gJ1snICsgaXRlbXMuam9pbignLCAnKSArICddJztcbn1cblxuZnVuY3Rpb24gZ2V0T2JqZWN0VGFnKG9iamVjdCkge1xuICBjb25zdCB0YWcgPSBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nXG4gICAgLmNhbGwob2JqZWN0KVxuICAgIC5yZXBsYWNlKC9eXFxbb2JqZWN0IC8sICcnKVxuICAgIC5yZXBsYWNlKC9dJC8sICcnKTtcblxuICBpZiAodGFnID09PSAnT2JqZWN0JyAmJiB0eXBlb2Ygb2JqZWN0LmNvbnN0cnVjdG9yID09PSAnZnVuY3Rpb24nKSB7XG4gICAgY29uc3QgbmFtZSA9IG9iamVjdC5jb25zdHJ1Y3Rvci5uYW1lO1xuXG4gICAgaWYgKHR5cGVvZiBuYW1lID09PSAnc3RyaW5nJyAmJiBuYW1lICE9PSAnJykge1xuICAgICAgcmV0dXJuIG5hbWU7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhZztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/jsutils/inspect.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/jsutils/instanceOf.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/graphql/jsutils/instanceOf.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   instanceOf: () => (/* binding */ instanceOf)\n/* harmony export */ });\n/* harmony import */ var _inspect_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./inspect.mjs */ \"(rsc)/./node_modules/graphql/jsutils/inspect.mjs\");\n\n/* c8 ignore next 3 */\n\nconst isProduction =\n  globalThis.process && // eslint-disable-next-line no-undef\n  \"development\" === 'production';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nconst instanceOf =\n  /* c8 ignore next 6 */\n  // FIXME: https://github.com/graphql/graphql-js/issues/2317\n  isProduction\n    ? function instanceOf(value, constructor) {\n        return value instanceof constructor;\n      }\n    : function instanceOf(value, constructor) {\n        if (value instanceof constructor) {\n          return true;\n        }\n\n        if (typeof value === 'object' && value !== null) {\n          var _value$constructor;\n\n          // Prefer Symbol.toStringTag since it is immune to minification.\n          const className = constructor.prototype[Symbol.toStringTag];\n          const valueClassName = // We still need to support constructor's name to detect conflicts with older versions of this library.\n            Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n              ? value[Symbol.toStringTag]\n              : (_value$constructor = value.constructor) === null ||\n                _value$constructor === void 0\n              ? void 0\n              : _value$constructor.name;\n\n          if (className === valueClassName) {\n            const stringifiedValue = (0,_inspect_mjs__WEBPACK_IMPORTED_MODULE_0__.inspect)(value);\n            throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n          }\n        }\n\n        return false;\n      };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/jsutils/instanceOf.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/jsutils/invariant.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/jsutils/invariant.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant)\n/* harmony export */ });\nfunction invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(\n      message != null ? message : 'Unexpected invariant triggered.',\n    );\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2ludmFyaWFudC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNhbmpheU1cXERlc2t0b3BcXEhBUlNIICAoYnRlY2ggY3NlKVxcaW5yZWFsXFxzdHVkZW50YWktbGFuZGluZ1xcbm9kZV9tb2R1bGVzXFxncmFwaHFsXFxqc3V0aWxzXFxpbnZhcmlhbnQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpbnZhcmlhbnQoY29uZGl0aW9uLCBtZXNzYWdlKSB7XG4gIGNvbnN0IGJvb2xlYW5Db25kaXRpb24gPSBCb29sZWFuKGNvbmRpdGlvbik7XG5cbiAgaWYgKCFib29sZWFuQ29uZGl0aW9uKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgbWVzc2FnZSAhPSBudWxsID8gbWVzc2FnZSA6ICdVbmV4cGVjdGVkIGludmFyaWFudCB0cmlnZ2VyZWQuJyxcbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/jsutils/invariant.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/jsutils/isObjectLike.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/graphql/jsutils/isObjectLike.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectLike: () => (/* binding */ isObjectLike)\n/* harmony export */ });\n/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nfunction isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9qc3V0aWxzL2lzT2JqZWN0TGlrZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFNhbmpheU1cXERlc2t0b3BcXEhBUlNIICAoYnRlY2ggY3NlKVxcaW5yZWFsXFxzdHVkZW50YWktbGFuZGluZ1xcbm9kZV9tb2R1bGVzXFxncmFwaHFsXFxqc3V0aWxzXFxpc09iamVjdExpa2UubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUmV0dXJuIHRydWUgaWYgYHZhbHVlYCBpcyBvYmplY3QtbGlrZS4gQSB2YWx1ZSBpcyBvYmplY3QtbGlrZSBpZiBpdCdzIG5vdFxuICogYG51bGxgIGFuZCBoYXMgYSBgdHlwZW9mYCByZXN1bHQgb2YgXCJvYmplY3RcIi5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzT2JqZWN0TGlrZSh2YWx1ZSkge1xuICByZXR1cm4gdHlwZW9mIHZhbHVlID09ICdvYmplY3QnICYmIHZhbHVlICE9PSBudWxsO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/jsutils/isObjectLike.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/ast.mjs":
/*!***********************************************!*\
  !*** ./node_modules/graphql/language/ast.mjs ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Location: () => (/* binding */ Location),\n/* harmony export */   OperationTypeNode: () => (/* binding */ OperationTypeNode),\n/* harmony export */   QueryDocumentKeys: () => (/* binding */ QueryDocumentKeys),\n/* harmony export */   Token: () => (/* binding */ Token),\n/* harmony export */   isNode: () => (/* binding */ isNode)\n/* harmony export */ });\n/**\n * Contains a range of UTF-8 character offsets and token references that\n * identify the region of the source from which the AST derived.\n */\nclass Location {\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The Token at which this Node begins.\n   */\n\n  /**\n   * The Token at which this Node ends.\n   */\n\n  /**\n   * The Source document the AST represents.\n   */\n  constructor(startToken, endToken, source) {\n    this.start = startToken.start;\n    this.end = endToken.end;\n    this.startToken = startToken;\n    this.endToken = endToken;\n    this.source = source;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Location';\n  }\n\n  toJSON() {\n    return {\n      start: this.start,\n      end: this.end,\n    };\n  }\n}\n/**\n * Represents a range of characters represented by a lexical token\n * within a Source.\n */\n\nclass Token {\n  /**\n   * The kind of Token.\n   */\n\n  /**\n   * The character offset at which this Node begins.\n   */\n\n  /**\n   * The character offset at which this Node ends.\n   */\n\n  /**\n   * The 1-indexed line number on which this Token appears.\n   */\n\n  /**\n   * The 1-indexed column number at which this Token begins.\n   */\n\n  /**\n   * For non-punctuation tokens, represents the interpreted value of the token.\n   *\n   * Note: is undefined for punctuation tokens, but typed as string for\n   * convenience in the parser.\n   */\n\n  /**\n   * Tokens exist as nodes in a double-linked-list amongst all tokens\n   * including ignored tokens. <SOF> is always the first node and <EOF>\n   * the last.\n   */\n  constructor(kind, start, end, line, column, value) {\n    this.kind = kind;\n    this.start = start;\n    this.end = end;\n    this.line = line;\n    this.column = column; // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\n    this.value = value;\n    this.prev = null;\n    this.next = null;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Token';\n  }\n\n  toJSON() {\n    return {\n      kind: this.kind,\n      value: this.value,\n      line: this.line,\n      column: this.column,\n    };\n  }\n}\n/**\n * The list of all possible AST node types.\n */\n\n/**\n * @internal\n */\nconst QueryDocumentKeys = {\n  Name: [],\n  Document: ['definitions'],\n  OperationDefinition: [\n    'name',\n    'variableDefinitions',\n    'directives',\n    'selectionSet',\n  ],\n  VariableDefinition: ['variable', 'type', 'defaultValue', 'directives'],\n  Variable: ['name'],\n  SelectionSet: ['selections'],\n  Field: ['alias', 'name', 'arguments', 'directives', 'selectionSet'],\n  Argument: ['name', 'value'],\n  FragmentSpread: ['name', 'directives'],\n  InlineFragment: ['typeCondition', 'directives', 'selectionSet'],\n  FragmentDefinition: [\n    'name', // Note: fragment variable definitions are deprecated and will removed in v17.0.0\n    'variableDefinitions',\n    'typeCondition',\n    'directives',\n    'selectionSet',\n  ],\n  IntValue: [],\n  FloatValue: [],\n  StringValue: [],\n  BooleanValue: [],\n  NullValue: [],\n  EnumValue: [],\n  ListValue: ['values'],\n  ObjectValue: ['fields'],\n  ObjectField: ['name', 'value'],\n  Directive: ['name', 'arguments'],\n  NamedType: ['name'],\n  ListType: ['type'],\n  NonNullType: ['type'],\n  SchemaDefinition: ['description', 'directives', 'operationTypes'],\n  OperationTypeDefinition: ['type'],\n  ScalarTypeDefinition: ['description', 'name', 'directives'],\n  ObjectTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  FieldDefinition: ['description', 'name', 'arguments', 'type', 'directives'],\n  InputValueDefinition: [\n    'description',\n    'name',\n    'type',\n    'defaultValue',\n    'directives',\n  ],\n  InterfaceTypeDefinition: [\n    'description',\n    'name',\n    'interfaces',\n    'directives',\n    'fields',\n  ],\n  UnionTypeDefinition: ['description', 'name', 'directives', 'types'],\n  EnumTypeDefinition: ['description', 'name', 'directives', 'values'],\n  EnumValueDefinition: ['description', 'name', 'directives'],\n  InputObjectTypeDefinition: ['description', 'name', 'directives', 'fields'],\n  DirectiveDefinition: ['description', 'name', 'arguments', 'locations'],\n  SchemaExtension: ['directives', 'operationTypes'],\n  ScalarTypeExtension: ['name', 'directives'],\n  ObjectTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  InterfaceTypeExtension: ['name', 'interfaces', 'directives', 'fields'],\n  UnionTypeExtension: ['name', 'directives', 'types'],\n  EnumTypeExtension: ['name', 'directives', 'values'],\n  InputObjectTypeExtension: ['name', 'directives', 'fields'],\n};\nconst kindValues = new Set(Object.keys(QueryDocumentKeys));\n/**\n * @internal\n */\n\nfunction isNode(maybeNode) {\n  const maybeKind =\n    maybeNode === null || maybeNode === void 0 ? void 0 : maybeNode.kind;\n  return typeof maybeKind === 'string' && kindValues.has(maybeKind);\n}\n/** Name */\n\nvar OperationTypeNode;\n\n(function (OperationTypeNode) {\n  OperationTypeNode['QUERY'] = 'query';\n  OperationTypeNode['MUTATION'] = 'mutation';\n  OperationTypeNode['SUBSCRIPTION'] = 'subscription';\n})(OperationTypeNode || (OperationTypeNode = {}));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/ast.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/blockString.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/graphql/language/blockString.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dedentBlockStringLines: () => (/* binding */ dedentBlockStringLines),\n/* harmony export */   isPrintableAsBlockString: () => (/* binding */ isPrintableAsBlockString),\n/* harmony export */   printBlockString: () => (/* binding */ printBlockString)\n/* harmony export */ });\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(rsc)/./node_modules/graphql/language/characterClasses.mjs\");\n\n/**\n * Produces the value of a block string from its parsed raw value, similar to\n * CoffeeScript's block string, Python's docstring trim or Ruby's strip_heredoc.\n *\n * This implements the GraphQL spec's BlockStringValue() static algorithm.\n *\n * @internal\n */\n\nfunction dedentBlockStringLines(lines) {\n  var _firstNonEmptyLine2;\n\n  let commonIndent = Number.MAX_SAFE_INTEGER;\n  let firstNonEmptyLine = null;\n  let lastNonEmptyLine = -1;\n\n  for (let i = 0; i < lines.length; ++i) {\n    var _firstNonEmptyLine;\n\n    const line = lines[i];\n    const indent = leadingWhitespace(line);\n\n    if (indent === line.length) {\n      continue; // skip empty lines\n    }\n\n    firstNonEmptyLine =\n      (_firstNonEmptyLine = firstNonEmptyLine) !== null &&\n      _firstNonEmptyLine !== void 0\n        ? _firstNonEmptyLine\n        : i;\n    lastNonEmptyLine = i;\n\n    if (i !== 0 && indent < commonIndent) {\n      commonIndent = indent;\n    }\n  }\n\n  return lines // Remove common indentation from all lines but first.\n    .map((line, i) => (i === 0 ? line : line.slice(commonIndent))) // Remove leading and trailing blank lines.\n    .slice(\n      (_firstNonEmptyLine2 = firstNonEmptyLine) !== null &&\n        _firstNonEmptyLine2 !== void 0\n        ? _firstNonEmptyLine2\n        : 0,\n      lastNonEmptyLine + 1,\n    );\n}\n\nfunction leadingWhitespace(str) {\n  let i = 0;\n\n  while (i < str.length && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(str.charCodeAt(i))) {\n    ++i;\n  }\n\n  return i;\n}\n/**\n * @internal\n */\n\nfunction isPrintableAsBlockString(value) {\n  if (value === '') {\n    return true; // empty string is printable\n  }\n\n  let isEmptyLine = true;\n  let hasIndent = false;\n  let hasCommonIndent = true;\n  let seenNonEmptyLine = false;\n\n  for (let i = 0; i < value.length; ++i) {\n    switch (value.codePointAt(i)) {\n      case 0x0000:\n      case 0x0001:\n      case 0x0002:\n      case 0x0003:\n      case 0x0004:\n      case 0x0005:\n      case 0x0006:\n      case 0x0007:\n      case 0x0008:\n      case 0x000b:\n      case 0x000c:\n      case 0x000e:\n      case 0x000f:\n        return false;\n      // Has non-printable characters\n\n      case 0x000d:\n        //  \\r\n        return false;\n      // Has \\r or \\r\\n which will be replaced as \\n\n\n      case 10:\n        //  \\n\n        if (isEmptyLine && !seenNonEmptyLine) {\n          return false; // Has leading new line\n        }\n\n        seenNonEmptyLine = true;\n        isEmptyLine = true;\n        hasIndent = false;\n        break;\n\n      case 9: //   \\t\n\n      case 32:\n        //  <space>\n        hasIndent || (hasIndent = isEmptyLine);\n        break;\n\n      default:\n        hasCommonIndent && (hasCommonIndent = hasIndent);\n        isEmptyLine = false;\n    }\n  }\n\n  if (isEmptyLine) {\n    return false; // Has trailing empty lines\n  }\n\n  if (hasCommonIndent && seenNonEmptyLine) {\n    return false; // Has internal indent\n  }\n\n  return true;\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n *\n * @internal\n */\n\nfunction printBlockString(value, options) {\n  const escapedValue = value.replace(/\"\"\"/g, '\\\\\"\"\"'); // Expand a block string's raw value into independent lines.\n\n  const lines = escapedValue.split(/\\r\\n|[\\n\\r]/g);\n  const isSingleLine = lines.length === 1; // If common indentation is found we can fix some of those cases by adding leading new line\n\n  const forceLeadingNewLine =\n    lines.length > 1 &&\n    lines\n      .slice(1)\n      .every((line) => line.length === 0 || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(line.charCodeAt(0))); // Trailing triple quotes just looks confusing but doesn't force trailing new line\n\n  const hasTrailingTripleQuotes = escapedValue.endsWith('\\\\\"\"\"'); // Trailing quote (single or double) or slash forces trailing new line\n\n  const hasTrailingQuote = value.endsWith('\"') && !hasTrailingTripleQuotes;\n  const hasTrailingSlash = value.endsWith('\\\\');\n  const forceTrailingNewline = hasTrailingQuote || hasTrailingSlash;\n  const printAsMultipleLines =\n    !(options !== null && options !== void 0 && options.minimize) && // add leading and trailing new lines only if it improves readability\n    (!isSingleLine ||\n      value.length > 70 ||\n      forceTrailingNewline ||\n      forceLeadingNewLine ||\n      hasTrailingTripleQuotes);\n  let result = ''; // Format a multi-line block quote to account for leading space.\n\n  const skipLeadingNewLine = isSingleLine && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(value.charCodeAt(0));\n\n  if ((printAsMultipleLines && !skipLeadingNewLine) || forceLeadingNewLine) {\n    result += '\\n';\n  }\n\n  result += escapedValue;\n\n  if (printAsMultipleLines || forceTrailingNewline) {\n    result += '\\n';\n  }\n\n  return '\"\"\"' + result + '\"\"\"';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/blockString.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/characterClasses.mjs":
/*!************************************************************!*\
  !*** ./node_modules/graphql/language/characterClasses.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDigit: () => (/* binding */ isDigit),\n/* harmony export */   isLetter: () => (/* binding */ isLetter),\n/* harmony export */   isNameContinue: () => (/* binding */ isNameContinue),\n/* harmony export */   isNameStart: () => (/* binding */ isNameStart),\n/* harmony export */   isWhiteSpace: () => (/* binding */ isWhiteSpace)\n/* harmony export */ });\n/**\n * ```\n * WhiteSpace ::\n *   - \"Horizontal Tab (U+0009)\"\n *   - \"Space (U+0020)\"\n * ```\n * @internal\n */\nfunction isWhiteSpace(code) {\n  return code === 0x0009 || code === 0x0020;\n}\n/**\n * ```\n * Digit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n * ```\n * @internal\n */\n\nfunction isDigit(code) {\n  return code >= 0x0030 && code <= 0x0039;\n}\n/**\n * ```\n * Letter :: one of\n *   - `A` `B` `C` `D` `E` `F` `G` `H` `I` `J` `K` `L` `M`\n *   - `N` `O` `P` `Q` `R` `S` `T` `U` `V` `W` `X` `Y` `Z`\n *   - `a` `b` `c` `d` `e` `f` `g` `h` `i` `j` `k` `l` `m`\n *   - `n` `o` `p` `q` `r` `s` `t` `u` `v` `w` `x` `y` `z`\n * ```\n * @internal\n */\n\nfunction isLetter(code) {\n  return (\n    (code >= 0x0061 && code <= 0x007a) || // A-Z\n    (code >= 0x0041 && code <= 0x005a) // a-z\n  );\n}\n/**\n * ```\n * NameStart ::\n *   - Letter\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameStart(code) {\n  return isLetter(code) || code === 0x005f;\n}\n/**\n * ```\n * NameContinue ::\n *   - Letter\n *   - Digit\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameContinue(code) {\n  return isLetter(code) || isDigit(code) || code === 0x005f;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/characterClasses.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/directiveLocation.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/graphql/language/directiveLocation.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectiveLocation: () => (/* binding */ DirectiveLocation)\n/* harmony export */ });\n/**\n * The set of allowed directive location values.\n */\nvar DirectiveLocation;\n\n(function (DirectiveLocation) {\n  DirectiveLocation['QUERY'] = 'QUERY';\n  DirectiveLocation['MUTATION'] = 'MUTATION';\n  DirectiveLocation['SUBSCRIPTION'] = 'SUBSCRIPTION';\n  DirectiveLocation['FIELD'] = 'FIELD';\n  DirectiveLocation['FRAGMENT_DEFINITION'] = 'FRAGMENT_DEFINITION';\n  DirectiveLocation['FRAGMENT_SPREAD'] = 'FRAGMENT_SPREAD';\n  DirectiveLocation['INLINE_FRAGMENT'] = 'INLINE_FRAGMENT';\n  DirectiveLocation['VARIABLE_DEFINITION'] = 'VARIABLE_DEFINITION';\n  DirectiveLocation['SCHEMA'] = 'SCHEMA';\n  DirectiveLocation['SCALAR'] = 'SCALAR';\n  DirectiveLocation['OBJECT'] = 'OBJECT';\n  DirectiveLocation['FIELD_DEFINITION'] = 'FIELD_DEFINITION';\n  DirectiveLocation['ARGUMENT_DEFINITION'] = 'ARGUMENT_DEFINITION';\n  DirectiveLocation['INTERFACE'] = 'INTERFACE';\n  DirectiveLocation['UNION'] = 'UNION';\n  DirectiveLocation['ENUM'] = 'ENUM';\n  DirectiveLocation['ENUM_VALUE'] = 'ENUM_VALUE';\n  DirectiveLocation['INPUT_OBJECT'] = 'INPUT_OBJECT';\n  DirectiveLocation['INPUT_FIELD_DEFINITION'] = 'INPUT_FIELD_DEFINITION';\n})(DirectiveLocation || (DirectiveLocation = {}));\n\n\n/**\n * The enum type representing the directive location values.\n *\n * @deprecated Please use `DirectiveLocation`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/directiveLocation.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/kinds.mjs":
/*!*************************************************!*\
  !*** ./node_modules/graphql/language/kinds.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Kind: () => (/* binding */ Kind)\n/* harmony export */ });\n/**\n * The set of allowed kind values for AST nodes.\n */\nvar Kind;\n\n(function (Kind) {\n  Kind['NAME'] = 'Name';\n  Kind['DOCUMENT'] = 'Document';\n  Kind['OPERATION_DEFINITION'] = 'OperationDefinition';\n  Kind['VARIABLE_DEFINITION'] = 'VariableDefinition';\n  Kind['SELECTION_SET'] = 'SelectionSet';\n  Kind['FIELD'] = 'Field';\n  Kind['ARGUMENT'] = 'Argument';\n  Kind['FRAGMENT_SPREAD'] = 'FragmentSpread';\n  Kind['INLINE_FRAGMENT'] = 'InlineFragment';\n  Kind['FRAGMENT_DEFINITION'] = 'FragmentDefinition';\n  Kind['VARIABLE'] = 'Variable';\n  Kind['INT'] = 'IntValue';\n  Kind['FLOAT'] = 'FloatValue';\n  Kind['STRING'] = 'StringValue';\n  Kind['BOOLEAN'] = 'BooleanValue';\n  Kind['NULL'] = 'NullValue';\n  Kind['ENUM'] = 'EnumValue';\n  Kind['LIST'] = 'ListValue';\n  Kind['OBJECT'] = 'ObjectValue';\n  Kind['OBJECT_FIELD'] = 'ObjectField';\n  Kind['DIRECTIVE'] = 'Directive';\n  Kind['NAMED_TYPE'] = 'NamedType';\n  Kind['LIST_TYPE'] = 'ListType';\n  Kind['NON_NULL_TYPE'] = 'NonNullType';\n  Kind['SCHEMA_DEFINITION'] = 'SchemaDefinition';\n  Kind['OPERATION_TYPE_DEFINITION'] = 'OperationTypeDefinition';\n  Kind['SCALAR_TYPE_DEFINITION'] = 'ScalarTypeDefinition';\n  Kind['OBJECT_TYPE_DEFINITION'] = 'ObjectTypeDefinition';\n  Kind['FIELD_DEFINITION'] = 'FieldDefinition';\n  Kind['INPUT_VALUE_DEFINITION'] = 'InputValueDefinition';\n  Kind['INTERFACE_TYPE_DEFINITION'] = 'InterfaceTypeDefinition';\n  Kind['UNION_TYPE_DEFINITION'] = 'UnionTypeDefinition';\n  Kind['ENUM_TYPE_DEFINITION'] = 'EnumTypeDefinition';\n  Kind['ENUM_VALUE_DEFINITION'] = 'EnumValueDefinition';\n  Kind['INPUT_OBJECT_TYPE_DEFINITION'] = 'InputObjectTypeDefinition';\n  Kind['DIRECTIVE_DEFINITION'] = 'DirectiveDefinition';\n  Kind['SCHEMA_EXTENSION'] = 'SchemaExtension';\n  Kind['SCALAR_TYPE_EXTENSION'] = 'ScalarTypeExtension';\n  Kind['OBJECT_TYPE_EXTENSION'] = 'ObjectTypeExtension';\n  Kind['INTERFACE_TYPE_EXTENSION'] = 'InterfaceTypeExtension';\n  Kind['UNION_TYPE_EXTENSION'] = 'UnionTypeExtension';\n  Kind['ENUM_TYPE_EXTENSION'] = 'EnumTypeExtension';\n  Kind['INPUT_OBJECT_TYPE_EXTENSION'] = 'InputObjectTypeExtension';\n})(Kind || (Kind = {}));\n\n\n/**\n * The enum type representing the possible kind values of AST nodes.\n *\n * @deprecated Please use `Kind`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/kinds.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/lexer.mjs":
/*!*************************************************!*\
  !*** ./node_modules/graphql/language/lexer.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Lexer: () => (/* binding */ Lexer),\n/* harmony export */   isPunctuatorTokenKind: () => (/* binding */ isPunctuatorTokenKind)\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(rsc)/./node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ast.mjs */ \"(rsc)/./node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _blockString_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blockString.mjs */ \"(rsc)/./node_modules/graphql/language/blockString.mjs\");\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(rsc)/./node_modules/graphql/language/characterClasses.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(rsc)/./node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nclass Lexer {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  constructor(source) {\n    const startOfFileToken = new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SOF, 0, 0, 0, 0);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Lexer';\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n  advance() {\n    this.lastToken = this.token;\n    const token = (this.token = this.lookahead());\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n\n  lookahead() {\n    let token = this.token;\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF) {\n      do {\n        if (token.next) {\n          token = token.next;\n        } else {\n          // Read the next token and form a link in the token linked-list.\n          const nextToken = readNextToken(this, token.end); // @ts-expect-error next is only mutable during parsing.\n\n          token.next = nextToken; // @ts-expect-error prev is only mutable during parsing.\n\n          nextToken.prev = token;\n          token = nextToken;\n        }\n      } while (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT);\n    }\n\n    return token;\n  }\n}\n/**\n * @internal\n */\n\nfunction isPunctuatorTokenKind(kind) {\n  return (\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R\n  );\n}\n/**\n * A Unicode scalar value is any Unicode code point except surrogate code\n * points. In other words, the inclusive ranges of values 0x0000 to 0xD7FF and\n * 0xE000 to 0x10FFFF.\n *\n * SourceCharacter ::\n *   - \"Any Unicode scalar value\"\n */\n\nfunction isUnicodeScalarValue(code) {\n  return (\n    (code >= 0x0000 && code <= 0xd7ff) || (code >= 0xe000 && code <= 0x10ffff)\n  );\n}\n/**\n * The GraphQL specification defines source text as a sequence of unicode scalar\n * values (which Unicode defines to exclude surrogate code points). However\n * JavaScript defines strings as a sequence of UTF-16 code units which may\n * include surrogates. A surrogate pair is a valid source character as it\n * encodes a supplementary code point (above U+FFFF), but unpaired surrogate\n * code points are not valid source characters.\n */\n\nfunction isSupplementaryCodePoint(body, location) {\n  return (\n    isLeadingSurrogate(body.charCodeAt(location)) &&\n    isTrailingSurrogate(body.charCodeAt(location + 1))\n  );\n}\n\nfunction isLeadingSurrogate(code) {\n  return code >= 0xd800 && code <= 0xdbff;\n}\n\nfunction isTrailingSurrogate(code) {\n  return code >= 0xdc00 && code <= 0xdfff;\n}\n/**\n * Prints the code point (or end of file reference) at a given location in a\n * source for use in error messages.\n *\n * Printable ASCII is printed quoted, while other points are printed in Unicode\n * code point form (ie. U+1234).\n */\n\nfunction printCodePointAt(lexer, location) {\n  const code = lexer.source.body.codePointAt(location);\n\n  if (code === undefined) {\n    return _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF;\n  } else if (code >= 0x0020 && code <= 0x007e) {\n    // Printable ASCII\n    const char = String.fromCodePoint(code);\n    return char === '\"' ? \"'\\\"'\" : `\"${char}\"`;\n  } // Unicode code point\n\n  return 'U+' + code.toString(16).toUpperCase().padStart(4, '0');\n}\n/**\n * Create a token with line and column location information.\n */\n\nfunction createToken(lexer, kind, start, end, value) {\n  const line = lexer.line;\n  const col = 1 + start - lexer.lineStart;\n  return new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(kind, start, end, line, col, value);\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\nfunction readNextToken(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // SourceCharacter\n\n    switch (code) {\n      // Ignored ::\n      //   - UnicodeBOM\n      //   - WhiteSpace\n      //   - LineTerminator\n      //   - Comment\n      //   - Comma\n      //\n      // UnicodeBOM :: \"Byte Order Mark (U+FEFF)\"\n      //\n      // WhiteSpace ::\n      //   - \"Horizontal Tab (U+0009)\"\n      //   - \"Space (U+0020)\"\n      //\n      // Comma :: ,\n      case 0xfeff: // <BOM>\n\n      case 0x0009: // \\t\n\n      case 0x0020: // <space>\n\n      case 0x002c:\n        // ,\n        ++position;\n        continue;\n      // LineTerminator ::\n      //   - \"New Line (U+000A)\"\n      //   - \"Carriage Return (U+000D)\" [lookahead != \"New Line (U+000A)\"]\n      //   - \"Carriage Return (U+000D)\" \"New Line (U+000A)\"\n\n      case 0x000a:\n        // \\n\n        ++position;\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n\n      case 0x000d:\n        // \\r\n        if (body.charCodeAt(position + 1) === 0x000a) {\n          position += 2;\n        } else {\n          ++position;\n        }\n\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      // Comment\n\n      case 0x0023:\n        // #\n        return readComment(lexer, position);\n      // Token ::\n      //   - Punctuator\n      //   - Name\n      //   - IntValue\n      //   - FloatValue\n      //   - StringValue\n      //\n      // Punctuator :: one of ! $ & ( ) ... : = @ [ ] { | }\n\n      case 0x0021:\n        // !\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG, position, position + 1);\n\n      case 0x0024:\n        // $\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR, position, position + 1);\n\n      case 0x0026:\n        // &\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP, position, position + 1);\n\n      case 0x0028:\n        // (\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L, position, position + 1);\n\n      case 0x0029:\n        // )\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R, position, position + 1);\n\n      case 0x002e:\n        // .\n        if (\n          body.charCodeAt(position + 1) === 0x002e &&\n          body.charCodeAt(position + 2) === 0x002e\n        ) {\n          return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD, position, position + 3);\n        }\n\n        break;\n\n      case 0x003a:\n        // :\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON, position, position + 1);\n\n      case 0x003d:\n        // =\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS, position, position + 1);\n\n      case 0x0040:\n        // @\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT, position, position + 1);\n\n      case 0x005b:\n        // [\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L, position, position + 1);\n\n      case 0x005d:\n        // ]\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R, position, position + 1);\n\n      case 0x007b:\n        // {\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L, position, position + 1);\n\n      case 0x007c:\n        // |\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE, position, position + 1);\n\n      case 0x007d:\n        // }\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R, position, position + 1);\n      // StringValue\n\n      case 0x0022:\n        // \"\n        if (\n          body.charCodeAt(position + 1) === 0x0022 &&\n          body.charCodeAt(position + 2) === 0x0022\n        ) {\n          return readBlockString(lexer, position);\n        }\n\n        return readString(lexer, position);\n    } // IntValue | FloatValue (Digit | -)\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code) || code === 0x002d) {\n      return readNumber(lexer, position, code);\n    } // Name\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n      return readName(lexer, position);\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      code === 0x0027\n        ? 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?'\n        : isUnicodeScalarValue(code) || isSupplementaryCodePoint(body, position)\n        ? `Unexpected character: ${printCodePointAt(lexer, position)}.`\n        : `Invalid character: ${printCodePointAt(lexer, position)}.`,\n    );\n  }\n\n  return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF, bodyLength, bodyLength);\n}\n/**\n * Reads a comment token from the source file.\n *\n * ```\n * Comment :: # CommentChar* [lookahead != CommentChar]\n *\n * CommentChar :: SourceCharacter but not LineTerminator\n * ```\n */\n\nfunction readComment(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT,\n    start,\n    position,\n    body.slice(start + 1, position),\n  );\n}\n/**\n * Reads a number token from the source file, either a FloatValue or an IntValue\n * depending on whether a FractionalPart or ExponentPart is encountered.\n *\n * ```\n * IntValue :: IntegerPart [lookahead != {Digit, `.`, NameStart}]\n *\n * IntegerPart ::\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit Digit*\n *\n * NegativeSign :: -\n *\n * NonZeroDigit :: Digit but not `0`\n *\n * FloatValue ::\n *   - IntegerPart FractionalPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart FractionalPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *\n * FractionalPart :: . Digit+\n *\n * ExponentPart :: ExponentIndicator Sign? Digit+\n *\n * ExponentIndicator :: one of `e` `E`\n *\n * Sign :: one of + -\n * ```\n */\n\nfunction readNumber(lexer, start, firstCode) {\n  const body = lexer.source.body;\n  let position = start;\n  let code = firstCode;\n  let isFloat = false; // NegativeSign (-)\n\n  if (code === 0x002d) {\n    code = body.charCodeAt(++position);\n  } // Zero (0)\n\n  if (code === 0x0030) {\n    code = body.charCodeAt(++position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code)) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid number, unexpected digit after 0: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  } else {\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Full stop (.)\n\n  if (code === 0x002e) {\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // E e\n\n  if (code === 0x0045 || code === 0x0065) {\n    isFloat = true;\n    code = body.charCodeAt(++position); // + -\n\n    if (code === 0x002b || code === 0x002d) {\n      code = body.charCodeAt(++position);\n    }\n\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n  if (code === 0x002e || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        position,\n      )}.`,\n    );\n  }\n\n  return createToken(\n    lexer,\n    isFloat ? _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.FLOAT : _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.INT,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n/**\n * Returns the new position in the source after reading one or more digits.\n */\n\nfunction readDigits(lexer, start, firstCode) {\n  if (!(0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(firstCode)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      start,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        start,\n      )}.`,\n    );\n  }\n\n  const body = lexer.source.body;\n  let position = start + 1; // +1 to skip first firstCode\n\n  while ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(body.charCodeAt(position))) {\n    ++position;\n  }\n\n  return position;\n}\n/**\n * Reads a single-quote string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"` [lookahead != `\"`]\n *   - `\"` StringCharacter+ `\"`\n *\n * StringCharacter ::\n *   - SourceCharacter but not `\"` or `\\` or LineTerminator\n *   - `\\u` EscapedUnicode\n *   - `\\` EscapedCharacter\n *\n * EscapedUnicode ::\n *   - `{` HexDigit+ `}`\n *   - HexDigit HexDigit HexDigit HexDigit\n *\n * EscapedCharacter :: one of `\"` `\\` `/` `b` `f` `n` `r` `t`\n * ```\n */\n\nfunction readString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  let chunkStart = position;\n  let value = '';\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Quote (\")\n\n    if (code === 0x0022) {\n      value += body.slice(chunkStart, position);\n      return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.STRING, start, position + 1, value);\n    } // Escape Sequence (\\)\n\n    if (code === 0x005c) {\n      value += body.slice(chunkStart, position);\n      const escape =\n        body.charCodeAt(position + 1) === 0x0075 // u\n          ? body.charCodeAt(position + 2) === 0x007b // {\n            ? readEscapedUnicodeVariableWidth(lexer, position)\n            : readEscapedUnicodeFixedWidth(lexer, position)\n          : readEscapedCharacter(lexer, position);\n      value += escape.value;\n      position += escape.size;\n      chunkStart = position;\n      continue;\n    } // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n} // The string value and lexed size of an escape sequence.\n\nfunction readEscapedUnicodeVariableWidth(lexer, position) {\n  const body = lexer.source.body;\n  let point = 0;\n  let size = 3; // Cannot be larger than 12 chars (\\u{00000000}).\n\n  while (size < 12) {\n    const code = body.charCodeAt(position + size++); // Closing Brace (})\n\n    if (code === 0x007d) {\n      // Must be at least 5 chars (\\u{0}) and encode a Unicode scalar value.\n      if (size < 5 || !isUnicodeScalarValue(point)) {\n        break;\n      }\n\n      return {\n        value: String.fromCodePoint(point),\n        size,\n      };\n    } // Append this hex digit to the code point.\n\n    point = (point << 4) | readHexDigit(code);\n\n    if (point < 0) {\n      break;\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(\n      position,\n      position + size,\n    )}\".`,\n  );\n}\n\nfunction readEscapedUnicodeFixedWidth(lexer, position) {\n  const body = lexer.source.body;\n  const code = read16BitHexCode(body, position + 2);\n\n  if (isUnicodeScalarValue(code)) {\n    return {\n      value: String.fromCodePoint(code),\n      size: 6,\n    };\n  } // GraphQL allows JSON-style surrogate pair escape sequences, but only when\n  // a valid pair is formed.\n\n  if (isLeadingSurrogate(code)) {\n    // \\u\n    if (\n      body.charCodeAt(position + 6) === 0x005c &&\n      body.charCodeAt(position + 7) === 0x0075\n    ) {\n      const trailingCode = read16BitHexCode(body, position + 8);\n\n      if (isTrailingSurrogate(trailingCode)) {\n        // JavaScript defines strings as a sequence of UTF-16 code units and\n        // encodes Unicode code points above U+FFFF using a surrogate pair of\n        // code units. Since this is a surrogate pair escape sequence, just\n        // include both codes into the JavaScript string value. Had JavaScript\n        // not been internally based on UTF-16, then this surrogate pair would\n        // be decoded to retrieve the supplementary code point.\n        return {\n          value: String.fromCodePoint(code, trailingCode),\n          size: 12,\n        };\n      }\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(position, position + 6)}\".`,\n  );\n}\n/**\n * Reads four hexadecimal characters and returns the positive integer that 16bit\n * hexadecimal string represents. For example, \"000f\" will return 15, and \"dead\"\n * will return 57005.\n *\n * Returns a negative number if any char was not a valid hexadecimal digit.\n */\n\nfunction read16BitHexCode(body, position) {\n  // readHexDigit() returns -1 on error. ORing a negative value with any other\n  // value always produces a negative value.\n  return (\n    (readHexDigit(body.charCodeAt(position)) << 12) |\n    (readHexDigit(body.charCodeAt(position + 1)) << 8) |\n    (readHexDigit(body.charCodeAt(position + 2)) << 4) |\n    readHexDigit(body.charCodeAt(position + 3))\n  );\n}\n/**\n * Reads a hexadecimal character and returns its positive integer value (0-15).\n *\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 if the provided character code was not a valid hexadecimal digit.\n *\n * HexDigit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n *   - `A` `B` `C` `D` `E` `F`\n *   - `a` `b` `c` `d` `e` `f`\n */\n\nfunction readHexDigit(code) {\n  return code >= 0x0030 && code <= 0x0039 // 0-9\n    ? code - 0x0030\n    : code >= 0x0041 && code <= 0x0046 // A-F\n    ? code - 0x0037\n    : code >= 0x0061 && code <= 0x0066 // a-f\n    ? code - 0x0057\n    : -1;\n}\n/**\n * | Escaped Character | Code Point | Character Name               |\n * | ----------------- | ---------- | ---------------------------- |\n * | `\"`               | U+0022     | double quote                 |\n * | `\\`               | U+005C     | reverse solidus (back slash) |\n * | `/`               | U+002F     | solidus (forward slash)      |\n * | `b`               | U+0008     | backspace                    |\n * | `f`               | U+000C     | form feed                    |\n * | `n`               | U+000A     | line feed (new line)         |\n * | `r`               | U+000D     | carriage return              |\n * | `t`               | U+0009     | horizontal tab               |\n */\n\nfunction readEscapedCharacter(lexer, position) {\n  const body = lexer.source.body;\n  const code = body.charCodeAt(position + 1);\n\n  switch (code) {\n    case 0x0022:\n      // \"\n      return {\n        value: '\\u0022',\n        size: 2,\n      };\n\n    case 0x005c:\n      // \\\n      return {\n        value: '\\u005c',\n        size: 2,\n      };\n\n    case 0x002f:\n      // /\n      return {\n        value: '\\u002f',\n        size: 2,\n      };\n\n    case 0x0062:\n      // b\n      return {\n        value: '\\u0008',\n        size: 2,\n      };\n\n    case 0x0066:\n      // f\n      return {\n        value: '\\u000c',\n        size: 2,\n      };\n\n    case 0x006e:\n      // n\n      return {\n        value: '\\u000a',\n        size: 2,\n      };\n\n    case 0x0072:\n      // r\n      return {\n        value: '\\u000d',\n        size: 2,\n      };\n\n    case 0x0074:\n      // t\n      return {\n        value: '\\u0009',\n        size: 2,\n      };\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid character escape sequence: \"${body.slice(\n      position,\n      position + 2,\n    )}\".`,\n  );\n}\n/**\n * Reads a block string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"\"` BlockStringCharacter* `\"\"\"`\n *\n * BlockStringCharacter ::\n *   - SourceCharacter but not `\"\"\"` or `\\\"\"\"`\n *   - `\\\"\"\"`\n * ```\n */\n\nfunction readBlockString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let lineStart = lexer.lineStart;\n  let position = start + 3;\n  let chunkStart = position;\n  let currentLine = '';\n  const blockLines = [];\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Triple-Quote (\"\"\")\n\n    if (\n      code === 0x0022 &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      const token = createToken(\n        lexer,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BLOCK_STRING,\n        start,\n        position + 3, // Return a string of the lines joined with U+000A.\n        (0,_blockString_mjs__WEBPACK_IMPORTED_MODULE_4__.dedentBlockStringLines)(blockLines).join('\\n'),\n      );\n      lexer.line += blockLines.length - 1;\n      lexer.lineStart = lineStart;\n      return token;\n    } // Escaped Triple-Quote (\\\"\"\")\n\n    if (\n      code === 0x005c &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022 &&\n      body.charCodeAt(position + 3) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      chunkStart = position + 1; // skip only slash\n\n      position += 4;\n      continue;\n    } // LineTerminator\n\n    if (code === 0x000a || code === 0x000d) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n\n      if (code === 0x000d && body.charCodeAt(position + 1) === 0x000a) {\n        position += 2;\n      } else {\n        ++position;\n      }\n\n      currentLine = '';\n      chunkStart = position;\n      lineStart = position;\n      continue;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * ```\n * Name ::\n *   - NameStart NameContinue* [lookahead != NameContinue]\n * ```\n */\n\nfunction readName(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameContinue)(code)) {\n      ++position;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.NAME,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/lexer.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/location.mjs":
/*!****************************************************!*\
  !*** ./node_modules/graphql/language/location.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocation: () => (/* binding */ getLocation)\n/* harmony export */ });\n/* harmony import */ var _jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/invariant.mjs */ \"(rsc)/./node_modules/graphql/jsutils/invariant.mjs\");\n\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nfunction getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || (0,_jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(false);\n\n    if (match.index >= position) {\n      break;\n    }\n\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n\n  return {\n    line,\n    column: position + 1 - lastLineStart,\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9sYW5ndWFnZS9sb2NhdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7O0FBRUE7QUFDQSx1Q0FBdUMsaUVBQVM7O0FBRWhEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTYW5qYXlNXFxEZXNrdG9wXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXGlucmVhbFxcc3R1ZGVudGFpLWxhbmRpbmdcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbFxcbGFuZ3VhZ2VcXGxvY2F0aW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICcuLi9qc3V0aWxzL2ludmFyaWFudC5tanMnO1xuY29uc3QgTGluZVJlZ0V4cCA9IC9cXHJcXG58W1xcblxccl0vZztcbi8qKlxuICogUmVwcmVzZW50cyBhIGxvY2F0aW9uIGluIGEgU291cmNlLlxuICovXG5cbi8qKlxuICogVGFrZXMgYSBTb3VyY2UgYW5kIGEgVVRGLTggY2hhcmFjdGVyIG9mZnNldCwgYW5kIHJldHVybnMgdGhlIGNvcnJlc3BvbmRpbmdcbiAqIGxpbmUgYW5kIGNvbHVtbiBhcyBhIFNvdXJjZUxvY2F0aW9uLlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0TG9jYXRpb24oc291cmNlLCBwb3NpdGlvbikge1xuICBsZXQgbGFzdExpbmVTdGFydCA9IDA7XG4gIGxldCBsaW5lID0gMTtcblxuICBmb3IgKGNvbnN0IG1hdGNoIG9mIHNvdXJjZS5ib2R5Lm1hdGNoQWxsKExpbmVSZWdFeHApKSB7XG4gICAgdHlwZW9mIG1hdGNoLmluZGV4ID09PSAnbnVtYmVyJyB8fCBpbnZhcmlhbnQoZmFsc2UpO1xuXG4gICAgaWYgKG1hdGNoLmluZGV4ID49IHBvc2l0aW9uKSB7XG4gICAgICBicmVhaztcbiAgICB9XG5cbiAgICBsYXN0TGluZVN0YXJ0ID0gbWF0Y2guaW5kZXggKyBtYXRjaFswXS5sZW5ndGg7XG4gICAgbGluZSArPSAxO1xuICB9XG5cbiAgcmV0dXJuIHtcbiAgICBsaW5lLFxuICAgIGNvbHVtbjogcG9zaXRpb24gKyAxIC0gbGFzdExpbmVTdGFydCxcbiAgfTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/location.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/parser.mjs":
/*!**************************************************!*\
  !*** ./node_modules/graphql/language/parser.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseConstValue: () => (/* binding */ parseConstValue),\n/* harmony export */   parseType: () => (/* binding */ parseType),\n/* harmony export */   parseValue: () => (/* binding */ parseValue)\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(rsc)/./node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ast.mjs */ \"(rsc)/./node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./directiveLocation.mjs */ \"(rsc)/./node_modules/graphql/language/directiveLocation.mjs\");\n/* harmony import */ var _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./kinds.mjs */ \"(rsc)/./node_modules/graphql/language/kinds.mjs\");\n/* harmony import */ var _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lexer.mjs */ \"(rsc)/./node_modules/graphql/language/lexer.mjs\");\n/* harmony import */ var _source_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./source.mjs */ \"(rsc)/./node_modules/graphql/language/source.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(rsc)/./node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n\n\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nfunction parse(source, options) {\n  const parser = new Parser(source, options);\n  const document = parser.parseDocument();\n  Object.defineProperty(document, 'tokenCount', {\n    enumerable: false,\n    value: parser.tokenCount,\n  });\n  return document;\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nfunction parseValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseValueLiteral(false);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Similar to parseValue(), but raises a parse error if it encounters a\n * variable. The return type will be a constant value.\n */\n\nfunction parseConstValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseConstValueLiteral();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nfunction parseType(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const type = parser.parseTypeReference();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nclass Parser {\n  constructor(source, options = {}) {\n    const sourceObj = (0,_source_mjs__WEBPACK_IMPORTED_MODULE_1__.isSource)(source) ? source : new _source_mjs__WEBPACK_IMPORTED_MODULE_1__.Source(source);\n    this._lexer = new _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.Lexer(sourceObj);\n    this._options = options;\n    this._tokenCounter = 0;\n  }\n\n  get tokenCount() {\n    return this._tokenCounter;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n  parseName() {\n    const token = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAME,\n      value: token.value,\n    });\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n\n  parseDocument() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DOCUMENT,\n      definitions: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF,\n        this.parseDefinition,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF,\n      ),\n    });\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   *\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n\n  parseDefinition() {\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } // Many definitions begin with a description and require a lookahead.\n\n    const hasDescription = this.peekDescription();\n    const keywordToken = hasDescription\n      ? this._lexer.lookahead()\n      : this._lexer.token;\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n\n        case 'type':\n          return this.parseObjectTypeDefinition();\n\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n\n        case 'union':\n          return this.parseUnionTypeDefinition();\n\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n\n      if (hasDescription) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          this._lexer.token.start,\n          'Unexpected description, descriptions are supported only on type definitions.',\n        );\n      }\n\n      switch (keywordToken.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n\n        case 'fragment':\n          return this.parseFragmentDefinition();\n\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n\n  parseOperationDefinition() {\n    const start = this._lexer.token;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n        operation: _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY,\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    const operation = this.parseOperationType();\n    let name;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      name = this.parseName();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n      operation,\n      name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n\n  parseOperationType() {\n    const operationToken = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n\n    switch (operationToken.value) {\n      case 'query':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY;\n\n      case 'mutation':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.MUTATION;\n\n      case 'subscription':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.SUBSCRIPTION;\n    }\n\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n\n  parseVariableDefinitions() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseVariableDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n\n  parseVariableDefinition() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n        ? this.parseConstValueLiteral()\n        : undefined,\n      directives: this.parseConstDirectives(),\n    });\n  }\n  /**\n   * Variable : $ Name\n   */\n\n  parseVariable() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE,\n      name: this.parseName(),\n    });\n  }\n  /**\n   * ```\n   * SelectionSet : { Selection+ }\n   * ```\n   */\n\n  parseSelectionSet() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SELECTION_SET,\n      selections: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n        this.parseSelection,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n      ),\n    });\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n\n  parseSelection() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD)\n      ? this.parseFragment()\n      : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n\n  parseField() {\n    const start = this._lexer.token;\n    const nameOrAlias = this.parseName();\n    let alias;\n    let name;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD,\n      alias,\n      name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)\n        ? this.parseSelectionSet()\n        : undefined,\n    });\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n\n  parseArguments(isConst) {\n    const item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n\n  parseArgument(isConst = false) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ARGUMENT,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  }\n\n  parseConstArgument() {\n    return this.parseArgument(true);\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n\n  parseFragment() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD);\n    const hasTypeCondition = this.expectOptionalKeyword('on');\n\n    if (!hasTypeCondition && this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n\n  parseFragmentDefinition() {\n    const start = this._lexer.token;\n    this.expectKeyword('fragment'); // Legacy support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (this._options.allowLegacyFragmentVariables === true) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n\n  parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseValueLiteral(isConst) {\n    const token = this._lexer.token;\n\n    switch (token.kind) {\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.INT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.FLOAT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FLOAT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING:\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME:\n        this.advanceLexer();\n\n        switch (token.value) {\n          case 'true':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: true,\n            });\n\n          case 'false':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: false,\n            });\n\n          case 'null':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NULL,\n            });\n\n          default:\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM,\n              value: token.value,\n            });\n        }\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR:\n        if (isConst) {\n          this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n\n          if (this._lexer.token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n            const varName = this._lexer.token.value;\n            throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n              this._lexer.source,\n              token.start,\n              `Unexpected variable \"$${varName}\" in constant value.`,\n            );\n          } else {\n            throw this.unexpected(token);\n          }\n        }\n\n        return this.parseVariable();\n\n      default:\n        throw this.unexpected();\n    }\n  }\n\n  parseConstValueLiteral() {\n    return this.parseValueLiteral(true);\n  }\n\n  parseStringLiteral() {\n    const token = this._lexer.token;\n    this.advanceLexer();\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.STRING,\n      value: token.value,\n      block: token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING,\n    });\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n\n  parseList(isConst) {\n    const item = () => this.parseValueLiteral(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST,\n      values: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R),\n    });\n  }\n  /**\n   * ```\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   * ```\n   */\n\n  parseObject(isConst) {\n    const item = () => this.parseObjectField(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT,\n      fields: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R),\n    });\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n\n  parseObjectField(isConst) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_FIELD,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n\n  parseDirectives(isConst) {\n    const directives = [];\n\n    while (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n\n    return directives;\n  }\n\n  parseConstDirectives() {\n    return this.parseDirectives(true);\n  }\n  /**\n   * ```\n   * Directive[Const] : @ Name Arguments[?Const]?\n   * ```\n   */\n\n  parseDirective(isConst) {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst),\n    });\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n\n  parseTypeReference() {\n    const start = this._lexer.token;\n    let type;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L)) {\n      const innerType = this.parseTypeReference();\n      this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R);\n      type = this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST_TYPE,\n        type: innerType,\n      });\n    } else {\n      type = this.parseNamedType();\n    }\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BANG)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NON_NULL_TYPE,\n        type,\n      });\n    }\n\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n\n  parseNamedType() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAMED_TYPE,\n      name: this.parseName(),\n    });\n  } // Implements the parsing rules in the Type Definition section.\n\n  peekDescription() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING) || this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n\n  parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * ```\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   * ```\n   */\n\n  parseSchemaDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.many(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_DEFINITION,\n      description,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n\n  parseOperationTypeDefinition() {\n    const start = this._lexer.token;\n    const operation = this.parseOperationType();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseNamedType();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_TYPE_DEFINITION,\n      operation,\n      type,\n    });\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n\n  parseScalarTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n\n  parseObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n\n  parseImplementsInterfaces() {\n    return this.expectOptionalKeyword('implements')\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AMP, this.parseNamedType)\n      : [];\n  }\n  /**\n   * ```\n   * FieldsDefinition : { FieldDefinition+ }\n   * ```\n   */\n\n  parseFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseFieldDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n\n  parseFieldDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      type,\n      directives,\n    });\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n\n  parseArgumentDefs() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n\n  parseInputValueDef() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    let defaultValue;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)) {\n      defaultValue = this.parseConstValueLiteral();\n    }\n\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_VALUE_DEFINITION,\n      description,\n      name,\n      type,\n      defaultValue,\n      directives,\n    });\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n\n  parseInterfaceTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n\n  parseUnionTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n\n  parseUnionMemberTypes() {\n    return this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseNamedType)\n      : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n\n  parseEnumTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * ```\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   * ```\n   */\n\n  parseEnumValuesDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseEnumValueDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   */\n\n  parseEnumValueDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseEnumValueName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_VALUE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseEnumValueName() {\n    if (\n      this._lexer.token.value === 'true' ||\n      this._lexer.token.value === 'false' ||\n      this._lexer.token.value === 'null'\n    ) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        this._lexer.token.start,\n        `${getTokenDesc(\n          this._lexer.token,\n        )} is reserved and cannot be used for an enum value.`,\n      );\n    }\n\n    return this.parseName();\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n\n  parseInputObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   * ```\n   */\n\n  parseInputFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n\n  parseTypeSystemExtension() {\n    const keywordToken = this._lexer.lookahead();\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n\n        case 'type':\n          return this.parseObjectTypeExtension();\n\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n\n        case 'union':\n          return this.parseUnionTypeExtension();\n\n        case 'enum':\n          return this.parseEnumTypeExtension();\n\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * ```\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   * ```\n   */\n\n  parseSchemaExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_EXTENSION,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n\n  parseScalarTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_EXTENSION,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n\n  parseObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n\n  parseInterfaceTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n\n  parseUnionTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_EXTENSION,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n\n  parseEnumTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_EXTENSION,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n\n  parseInputObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   * ```\n   */\n\n  parseDirectiveDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    const repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    const locations = this.parseDirectiveLocations();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations,\n    });\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n\n  parseDirectiveLocations() {\n    return this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n\n  parseDirectiveLocation() {\n    const start = this._lexer.token;\n    const name = this.parseName();\n\n    if (Object.prototype.hasOwnProperty.call(_directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__.DirectiveLocation, name.value)) {\n      return name;\n    }\n\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a node that, if configured to do so, sets a \"loc\" field as a\n   * location object, used to identify the place in the source that created a\n   * given parsed object.\n   */\n\n  node(startToken, node) {\n    if (this._options.noLocation !== true) {\n      node.loc = new _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.Location(\n        startToken,\n        this._lexer.lastToken,\n        this._lexer.source,\n      );\n    }\n\n    return node;\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n\n  peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return token;\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Expected ${getTokenKindDesc(kind)}, found ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * If the next token is of the given kind, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        token.start,\n        `Expected \"${value}\", found ${getTokenDesc(token)}.`,\n      );\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n\n  unexpected(atToken) {\n    const token =\n      atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Unexpected ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      const nodes = [];\n\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n\n      return nodes;\n    }\n\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n\n  delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n\n    return nodes;\n  }\n\n  advanceLexer() {\n    const { maxTokens } = this._options;\n\n    const token = this._lexer.advance();\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF) {\n      ++this._tokenCounter;\n\n      if (maxTokens !== undefined && this._tokenCounter > maxTokens) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          token.start,\n          `Document contains more that ${maxTokens} tokens. Parsing aborted.`,\n        );\n      }\n    }\n  }\n}\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  const value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? ` \"${value}\"` : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\nfunction getTokenKindDesc(kind) {\n  return (0,_lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPunctuatorTokenKind)(kind) ? `\"${kind}\"` : kind;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/parser.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/printLocation.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/graphql/language/printLocation.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printLocation: () => (/* binding */ printLocation),\n/* harmony export */   printSourceLocation: () => (/* binding */ printSourceLocation)\n/* harmony export */ });\n/* harmony import */ var _location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./location.mjs */ \"(rsc)/./node_modules/graphql/language/location.mjs\");\n\n\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\nfunction printLocation(location) {\n  return printSourceLocation(\n    location.source,\n    (0,_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(location.source, location.start),\n  );\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nfunction printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n\n    return (\n      locationStr +\n      printPrefixedLines([\n        [`${lineNum} |`, subLines[0]],\n        ...subLines.slice(1, subLineIndex + 1).map((subLine) => ['|', subLine]),\n        ['|', '^'.padStart(subLineColumnNum)],\n        ['|', subLines[subLineIndex + 1]],\n      ])\n    );\n  }\n\n  return (\n    locationStr +\n    printPrefixedLines([\n      // Lines specified like this: [\"prefix\", \"string\"],\n      [`${lineNum - 1} |`, lines[lineIndex - 1]],\n      [`${lineNum} |`, locationLine],\n      ['|', '^'.padStart(columnNum)],\n      [`${lineNum + 1} |`, lines[lineIndex + 1]],\n    ])\n  );\n}\n\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines\n    .map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : ''))\n    .join('\\n');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/printLocation.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/printString.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/graphql/language/printString.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printString: () => (/* binding */ printString)\n/* harmony export */ });\n/**\n * Prints a string as a GraphQL StringValue literal. Replaces control characters\n * and excluded characters (\" U+0022 and \\\\ U+005C) with escape sequences.\n */\nfunction printString(str) {\n  return `\"${str.replace(escapedRegExp, escapedReplacer)}\"`;\n} // eslint-disable-next-line no-control-regex\n\nconst escapedRegExp = /[\\x00-\\x1f\\x22\\x5c\\x7f-\\x9f]/g;\n\nfunction escapedReplacer(str) {\n  return escapeSequences[str.charCodeAt(0)];\n} // prettier-ignore\n\nconst escapeSequences = [\n  '\\\\u0000',\n  '\\\\u0001',\n  '\\\\u0002',\n  '\\\\u0003',\n  '\\\\u0004',\n  '\\\\u0005',\n  '\\\\u0006',\n  '\\\\u0007',\n  '\\\\b',\n  '\\\\t',\n  '\\\\n',\n  '\\\\u000B',\n  '\\\\f',\n  '\\\\r',\n  '\\\\u000E',\n  '\\\\u000F',\n  '\\\\u0010',\n  '\\\\u0011',\n  '\\\\u0012',\n  '\\\\u0013',\n  '\\\\u0014',\n  '\\\\u0015',\n  '\\\\u0016',\n  '\\\\u0017',\n  '\\\\u0018',\n  '\\\\u0019',\n  '\\\\u001A',\n  '\\\\u001B',\n  '\\\\u001C',\n  '\\\\u001D',\n  '\\\\u001E',\n  '\\\\u001F',\n  '',\n  '',\n  '\\\\\"',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 2F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 3F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 4F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\\\\\',\n  '',\n  '',\n  '', // 5F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 6F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\u007F',\n  '\\\\u0080',\n  '\\\\u0081',\n  '\\\\u0082',\n  '\\\\u0083',\n  '\\\\u0084',\n  '\\\\u0085',\n  '\\\\u0086',\n  '\\\\u0087',\n  '\\\\u0088',\n  '\\\\u0089',\n  '\\\\u008A',\n  '\\\\u008B',\n  '\\\\u008C',\n  '\\\\u008D',\n  '\\\\u008E',\n  '\\\\u008F',\n  '\\\\u0090',\n  '\\\\u0091',\n  '\\\\u0092',\n  '\\\\u0093',\n  '\\\\u0094',\n  '\\\\u0095',\n  '\\\\u0096',\n  '\\\\u0097',\n  '\\\\u0098',\n  '\\\\u0099',\n  '\\\\u009A',\n  '\\\\u009B',\n  '\\\\u009C',\n  '\\\\u009D',\n  '\\\\u009E',\n  '\\\\u009F',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/printString.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/printer.mjs":
/*!***************************************************!*\
  !*** ./node_modules/graphql/language/printer.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   print: () => (/* binding */ print)\n/* harmony export */ });\n/* harmony import */ var _blockString_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blockString.mjs */ \"(rsc)/./node_modules/graphql/language/blockString.mjs\");\n/* harmony import */ var _printString_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./printString.mjs */ \"(rsc)/./node_modules/graphql/language/printString.mjs\");\n/* harmony import */ var _visitor_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./visitor.mjs */ \"(rsc)/./node_modules/graphql/language/visitor.mjs\");\n\n\n\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n */\n\nfunction print(ast) {\n  return (0,_visitor_mjs__WEBPACK_IMPORTED_MODULE_0__.visit)(ast, printDocASTReducer);\n}\nconst MAX_LINE_LENGTH = 80;\nconst printDocASTReducer = {\n  Name: {\n    leave: (node) => node.value,\n  },\n  Variable: {\n    leave: (node) => '$' + node.name,\n  },\n  // Document\n  Document: {\n    leave: (node) => join(node.definitions, '\\n\\n'),\n  },\n  OperationDefinition: {\n    leave(node) {\n      const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');\n      const prefix = join(\n        [\n          node.operation,\n          join([node.name, varDefs]),\n          join(node.directives, ' '),\n        ],\n        ' ',\n      ); // Anonymous queries with no directives or variable definitions can use\n      // the query short form.\n\n      return (prefix === 'query' ? '' : prefix + ' ') + node.selectionSet;\n    },\n  },\n  VariableDefinition: {\n    leave: ({ variable, type, defaultValue, directives }) =>\n      variable +\n      ': ' +\n      type +\n      wrap(' = ', defaultValue) +\n      wrap(' ', join(directives, ' ')),\n  },\n  SelectionSet: {\n    leave: ({ selections }) => block(selections),\n  },\n  Field: {\n    leave({ alias, name, arguments: args, directives, selectionSet }) {\n      const prefix = wrap('', alias, ': ') + name;\n      let argsLine = prefix + wrap('(', join(args, ', '), ')');\n\n      if (argsLine.length > MAX_LINE_LENGTH) {\n        argsLine = prefix + wrap('(\\n', indent(join(args, '\\n')), '\\n)');\n      }\n\n      return join([argsLine, join(directives, ' '), selectionSet], ' ');\n    },\n  },\n  Argument: {\n    leave: ({ name, value }) => name + ': ' + value,\n  },\n  // Fragments\n  FragmentSpread: {\n    leave: ({ name, directives }) =>\n      '...' + name + wrap(' ', join(directives, ' ')),\n  },\n  InlineFragment: {\n    leave: ({ typeCondition, directives, selectionSet }) =>\n      join(\n        [\n          '...',\n          wrap('on ', typeCondition),\n          join(directives, ' '),\n          selectionSet,\n        ],\n        ' ',\n      ),\n  },\n  FragmentDefinition: {\n    leave: (\n      { name, typeCondition, variableDefinitions, directives, selectionSet }, // Note: fragment variable definitions are experimental and may be changed\n    ) =>\n      // or removed in the future.\n      `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` +\n      `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` +\n      selectionSet,\n  },\n  // Value\n  IntValue: {\n    leave: ({ value }) => value,\n  },\n  FloatValue: {\n    leave: ({ value }) => value,\n  },\n  StringValue: {\n    leave: ({ value, block: isBlockString }) =>\n      isBlockString ? (0,_blockString_mjs__WEBPACK_IMPORTED_MODULE_1__.printBlockString)(value) : (0,_printString_mjs__WEBPACK_IMPORTED_MODULE_2__.printString)(value),\n  },\n  BooleanValue: {\n    leave: ({ value }) => (value ? 'true' : 'false'),\n  },\n  NullValue: {\n    leave: () => 'null',\n  },\n  EnumValue: {\n    leave: ({ value }) => value,\n  },\n  ListValue: {\n    leave: ({ values }) => '[' + join(values, ', ') + ']',\n  },\n  ObjectValue: {\n    leave: ({ fields }) => '{' + join(fields, ', ') + '}',\n  },\n  ObjectField: {\n    leave: ({ name, value }) => name + ': ' + value,\n  },\n  // Directive\n  Directive: {\n    leave: ({ name, arguments: args }) =>\n      '@' + name + wrap('(', join(args, ', '), ')'),\n  },\n  // Type\n  NamedType: {\n    leave: ({ name }) => name,\n  },\n  ListType: {\n    leave: ({ type }) => '[' + type + ']',\n  },\n  NonNullType: {\n    leave: ({ type }) => type + '!',\n  },\n  // Type System Definitions\n  SchemaDefinition: {\n    leave: ({ description, directives, operationTypes }) =>\n      wrap('', description, '\\n') +\n      join(['schema', join(directives, ' '), block(operationTypes)], ' '),\n  },\n  OperationTypeDefinition: {\n    leave: ({ operation, type }) => operation + ': ' + type,\n  },\n  ScalarTypeDefinition: {\n    leave: ({ description, name, directives }) =>\n      wrap('', description, '\\n') +\n      join(['scalar', name, join(directives, ' ')], ' '),\n  },\n  ObjectTypeDefinition: {\n    leave: ({ description, name, interfaces, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(\n        [\n          'type',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  FieldDefinition: {\n    leave: ({ description, name, arguments: args, type, directives }) =>\n      wrap('', description, '\\n') +\n      name +\n      (hasMultilineItems(args)\n        ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n        : wrap('(', join(args, ', '), ')')) +\n      ': ' +\n      type +\n      wrap(' ', join(directives, ' ')),\n  },\n  InputValueDefinition: {\n    leave: ({ description, name, type, defaultValue, directives }) =>\n      wrap('', description, '\\n') +\n      join(\n        [name + ': ' + type, wrap('= ', defaultValue), join(directives, ' ')],\n        ' ',\n      ),\n  },\n  InterfaceTypeDefinition: {\n    leave: ({ description, name, interfaces, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(\n        [\n          'interface',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  UnionTypeDefinition: {\n    leave: ({ description, name, directives, types }) =>\n      wrap('', description, '\\n') +\n      join(\n        ['union', name, join(directives, ' '), wrap('= ', join(types, ' | '))],\n        ' ',\n      ),\n  },\n  EnumTypeDefinition: {\n    leave: ({ description, name, directives, values }) =>\n      wrap('', description, '\\n') +\n      join(['enum', name, join(directives, ' '), block(values)], ' '),\n  },\n  EnumValueDefinition: {\n    leave: ({ description, name, directives }) =>\n      wrap('', description, '\\n') + join([name, join(directives, ' ')], ' '),\n  },\n  InputObjectTypeDefinition: {\n    leave: ({ description, name, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(['input', name, join(directives, ' '), block(fields)], ' '),\n  },\n  DirectiveDefinition: {\n    leave: ({ description, name, arguments: args, repeatable, locations }) =>\n      wrap('', description, '\\n') +\n      'directive @' +\n      name +\n      (hasMultilineItems(args)\n        ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n        : wrap('(', join(args, ', '), ')')) +\n      (repeatable ? ' repeatable' : '') +\n      ' on ' +\n      join(locations, ' | '),\n  },\n  SchemaExtension: {\n    leave: ({ directives, operationTypes }) =>\n      join(\n        ['extend schema', join(directives, ' '), block(operationTypes)],\n        ' ',\n      ),\n  },\n  ScalarTypeExtension: {\n    leave: ({ name, directives }) =>\n      join(['extend scalar', name, join(directives, ' ')], ' '),\n  },\n  ObjectTypeExtension: {\n    leave: ({ name, interfaces, directives, fields }) =>\n      join(\n        [\n          'extend type',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  InterfaceTypeExtension: {\n    leave: ({ name, interfaces, directives, fields }) =>\n      join(\n        [\n          'extend interface',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  UnionTypeExtension: {\n    leave: ({ name, directives, types }) =>\n      join(\n        [\n          'extend union',\n          name,\n          join(directives, ' '),\n          wrap('= ', join(types, ' | ')),\n        ],\n        ' ',\n      ),\n  },\n  EnumTypeExtension: {\n    leave: ({ name, directives, values }) =>\n      join(['extend enum', name, join(directives, ' '), block(values)], ' '),\n  },\n  InputObjectTypeExtension: {\n    leave: ({ name, directives, fields }) =>\n      join(['extend input', name, join(directives, ' '), block(fields)], ' '),\n  },\n};\n/**\n * Given maybeArray, print an empty string if it is null or empty, otherwise\n * print all items together separated by separator if provided\n */\n\nfunction join(maybeArray, separator = '') {\n  var _maybeArray$filter$jo;\n\n  return (_maybeArray$filter$jo =\n    maybeArray === null || maybeArray === void 0\n      ? void 0\n      : maybeArray.filter((x) => x).join(separator)) !== null &&\n    _maybeArray$filter$jo !== void 0\n    ? _maybeArray$filter$jo\n    : '';\n}\n/**\n * Given array, print each item on its own line, wrapped in an indented `{ }` block.\n */\n\nfunction block(array) {\n  return wrap('{\\n', indent(join(array, '\\n')), '\\n}');\n}\n/**\n * If maybeString is not null or empty, then wrap with start and end, otherwise print an empty string.\n */\n\nfunction wrap(start, maybeString, end = '') {\n  return maybeString != null && maybeString !== ''\n    ? start + maybeString + end\n    : '';\n}\n\nfunction indent(str) {\n  return wrap('  ', str.replace(/\\n/g, '\\n  '));\n}\n\nfunction hasMultilineItems(maybeArray) {\n  var _maybeArray$some;\n\n  // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n  return (_maybeArray$some =\n    maybeArray === null || maybeArray === void 0\n      ? void 0\n      : maybeArray.some((str) => str.includes('\\n'))) !== null &&\n    _maybeArray$some !== void 0\n    ? _maybeArray$some\n    : false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/printer.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/source.mjs":
/*!**************************************************!*\
  !*** ./node_modules/graphql/language/source.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Source: () => (/* binding */ Source),\n/* harmony export */   isSource: () => (/* binding */ isSource)\n/* harmony export */ });\n/* harmony import */ var _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/devAssert.mjs */ \"(rsc)/./node_modules/graphql/jsutils/devAssert.mjs\");\n/* harmony import */ var _jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/inspect.mjs */ \"(rsc)/./node_modules/graphql/jsutils/inspect.mjs\");\n/* harmony import */ var _jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../jsutils/instanceOf.mjs */ \"(rsc)/./node_modules/graphql/jsutils/instanceOf.mjs\");\n\n\n\n\n/**\n * A representation of source input to GraphQL. The `name` and `locationOffset` parameters are\n * optional, but they are useful for clients who store GraphQL documents in source files.\n * For example, if the GraphQL input starts at line 40 in a file named `Foo.graphql`, it might\n * be useful for `name` to be `\"Foo.graphql\"` and location to be `{ line: 40, column: 1 }`.\n * The `line` and `column` properties in `locationOffset` are 1-indexed.\n */\nclass Source {\n  constructor(\n    body,\n    name = 'GraphQL request',\n    locationOffset = {\n      line: 1,\n      column: 1,\n    },\n  ) {\n    typeof body === 'string' ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(false, `Body must be a string. Received: ${(0,_jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__.inspect)(body)}.`);\n    this.body = body;\n    this.name = name;\n    this.locationOffset = locationOffset;\n    this.locationOffset.line > 0 ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(\n        false,\n        'line in locationOffset is 1-indexed and must be positive.',\n      );\n    this.locationOffset.column > 0 ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(\n        false,\n        'column in locationOffset is 1-indexed and must be positive.',\n      );\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Source';\n  }\n}\n/**\n * Test if the given value is a Source object.\n *\n * @internal\n */\n\nfunction isSource(source) {\n  return (0,_jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__.instanceOf)(source, Source);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/source.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/tokenKind.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/graphql/language/tokenKind.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenKind: () => (/* binding */ TokenKind)\n/* harmony export */ });\n/**\n * An exported enum describing the different kinds of tokens that the\n * lexer emits.\n */\nvar TokenKind;\n\n(function (TokenKind) {\n  TokenKind['SOF'] = '<SOF>';\n  TokenKind['EOF'] = '<EOF>';\n  TokenKind['BANG'] = '!';\n  TokenKind['DOLLAR'] = '$';\n  TokenKind['AMP'] = '&';\n  TokenKind['PAREN_L'] = '(';\n  TokenKind['PAREN_R'] = ')';\n  TokenKind['SPREAD'] = '...';\n  TokenKind['COLON'] = ':';\n  TokenKind['EQUALS'] = '=';\n  TokenKind['AT'] = '@';\n  TokenKind['BRACKET_L'] = '[';\n  TokenKind['BRACKET_R'] = ']';\n  TokenKind['BRACE_L'] = '{';\n  TokenKind['PIPE'] = '|';\n  TokenKind['BRACE_R'] = '}';\n  TokenKind['NAME'] = 'Name';\n  TokenKind['INT'] = 'Int';\n  TokenKind['FLOAT'] = 'Float';\n  TokenKind['STRING'] = 'String';\n  TokenKind['BLOCK_STRING'] = 'BlockString';\n  TokenKind['COMMENT'] = 'Comment';\n})(TokenKind || (TokenKind = {}));\n\n\n/**\n * The enum type representing the token kinds values.\n *\n * @deprecated Please use `TokenKind`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JhcGhxbC9sYW5ndWFnZS90b2tlbktpbmQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyw4QkFBOEI7O0FBRVY7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxTYW5qYXlNXFxEZXNrdG9wXFxIQVJTSCAgKGJ0ZWNoIGNzZSlcXGlucmVhbFxcc3R1ZGVudGFpLWxhbmRpbmdcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbFxcbGFuZ3VhZ2VcXHRva2VuS2luZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBbiBleHBvcnRlZCBlbnVtIGRlc2NyaWJpbmcgdGhlIGRpZmZlcmVudCBraW5kcyBvZiB0b2tlbnMgdGhhdCB0aGVcbiAqIGxleGVyIGVtaXRzLlxuICovXG52YXIgVG9rZW5LaW5kO1xuXG4oZnVuY3Rpb24gKFRva2VuS2luZCkge1xuICBUb2tlbktpbmRbJ1NPRiddID0gJzxTT0Y+JztcbiAgVG9rZW5LaW5kWydFT0YnXSA9ICc8RU9GPic7XG4gIFRva2VuS2luZFsnQkFORyddID0gJyEnO1xuICBUb2tlbktpbmRbJ0RPTExBUiddID0gJyQnO1xuICBUb2tlbktpbmRbJ0FNUCddID0gJyYnO1xuICBUb2tlbktpbmRbJ1BBUkVOX0wnXSA9ICcoJztcbiAgVG9rZW5LaW5kWydQQVJFTl9SJ10gPSAnKSc7XG4gIFRva2VuS2luZFsnU1BSRUFEJ10gPSAnLi4uJztcbiAgVG9rZW5LaW5kWydDT0xPTiddID0gJzonO1xuICBUb2tlbktpbmRbJ0VRVUFMUyddID0gJz0nO1xuICBUb2tlbktpbmRbJ0FUJ10gPSAnQCc7XG4gIFRva2VuS2luZFsnQlJBQ0tFVF9MJ10gPSAnWyc7XG4gIFRva2VuS2luZFsnQlJBQ0tFVF9SJ10gPSAnXSc7XG4gIFRva2VuS2luZFsnQlJBQ0VfTCddID0gJ3snO1xuICBUb2tlbktpbmRbJ1BJUEUnXSA9ICd8JztcbiAgVG9rZW5LaW5kWydCUkFDRV9SJ10gPSAnfSc7XG4gIFRva2VuS2luZFsnTkFNRSddID0gJ05hbWUnO1xuICBUb2tlbktpbmRbJ0lOVCddID0gJ0ludCc7XG4gIFRva2VuS2luZFsnRkxPQVQnXSA9ICdGbG9hdCc7XG4gIFRva2VuS2luZFsnU1RSSU5HJ10gPSAnU3RyaW5nJztcbiAgVG9rZW5LaW5kWydCTE9DS19TVFJJTkcnXSA9ICdCbG9ja1N0cmluZyc7XG4gIFRva2VuS2luZFsnQ09NTUVOVCddID0gJ0NvbW1lbnQnO1xufSkoVG9rZW5LaW5kIHx8IChUb2tlbktpbmQgPSB7fSkpO1xuXG5leHBvcnQgeyBUb2tlbktpbmQgfTtcbi8qKlxuICogVGhlIGVudW0gdHlwZSByZXByZXNlbnRpbmcgdGhlIHRva2VuIGtpbmRzIHZhbHVlcy5cbiAqXG4gKiBAZGVwcmVjYXRlZCBQbGVhc2UgdXNlIGBUb2tlbktpbmRgLiBXaWxsIGJlIHJlbW92ZSBpbiB2MTcuXG4gKi9cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/tokenKind.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/graphql/language/visitor.mjs":
/*!***************************************************!*\
  !*** ./node_modules/graphql/language/visitor.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BREAK: () => (/* binding */ BREAK),\n/* harmony export */   getEnterLeaveForKind: () => (/* binding */ getEnterLeaveForKind),\n/* harmony export */   getVisitFn: () => (/* binding */ getVisitFn),\n/* harmony export */   visit: () => (/* binding */ visit),\n/* harmony export */   visitInParallel: () => (/* binding */ visitInParallel)\n/* harmony export */ });\n/* harmony import */ var _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../jsutils/devAssert.mjs */ \"(rsc)/./node_modules/graphql/jsutils/devAssert.mjs\");\n/* harmony import */ var _jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../jsutils/inspect.mjs */ \"(rsc)/./node_modules/graphql/jsutils/inspect.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ast.mjs */ \"(rsc)/./node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _kinds_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./kinds.mjs */ \"(rsc)/./node_modules/graphql/language/kinds.mjs\");\n\n\n\n\n/**\n * A visitor is provided to visit, it contains the collection of\n * relevant functions to be called during the visitor's traversal.\n */\n\nconst BREAK = Object.freeze({});\n/**\n * visit() will walk through an AST using a depth-first traversal, calling\n * the visitor's enter function at each node in the traversal, and calling the\n * leave function after visiting that node and all of its child nodes.\n *\n * By returning different values from the enter and leave functions, the\n * behavior of the visitor can be altered, including skipping over a sub-tree of\n * the AST (by returning false), editing the AST by returning a value or null\n * to remove the value, or to stop the whole traversal by returning BREAK.\n *\n * When using visit() to edit an AST, the original AST will not be modified, and\n * a new version of the AST with the changes applied will be returned from the\n * visit function.\n *\n * ```ts\n * const editedAST = visit(ast, {\n *   enter(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: skip visiting this node\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   },\n *   leave(node, key, parent, path, ancestors) {\n *     // @return\n *     //   undefined: no action\n *     //   false: no action\n *     //   visitor.BREAK: stop visiting altogether\n *     //   null: delete this node\n *     //   any value: replace this node with the returned value\n *   }\n * });\n * ```\n *\n * Alternatively to providing enter() and leave() functions, a visitor can\n * instead provide functions named the same as the kinds of AST nodes, or\n * enter/leave visitors at a named key, leading to three permutations of the\n * visitor API:\n *\n * 1) Named visitors triggered when entering a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind(node) {\n *     // enter the \"Kind\" node\n *   }\n * })\n * ```\n *\n * 2) Named visitors that trigger upon entering and leaving a node of a specific kind.\n *\n * ```ts\n * visit(ast, {\n *   Kind: {\n *     enter(node) {\n *       // enter the \"Kind\" node\n *     }\n *     leave(node) {\n *       // leave the \"Kind\" node\n *     }\n *   }\n * })\n * ```\n *\n * 3) Generic visitors that trigger upon entering and leaving any node.\n *\n * ```ts\n * visit(ast, {\n *   enter(node) {\n *     // enter any node\n *   },\n *   leave(node) {\n *     // leave any node\n *   }\n * })\n * ```\n */\n\nfunction visit(root, visitor, visitorKeys = _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.QueryDocumentKeys) {\n  const enterLeaveMap = new Map();\n\n  for (const kind of Object.values(_kinds_mjs__WEBPACK_IMPORTED_MODULE_1__.Kind)) {\n    enterLeaveMap.set(kind, getEnterLeaveForKind(visitor, kind));\n  }\n  /* eslint-disable no-undef-init */\n\n  let stack = undefined;\n  let inArray = Array.isArray(root);\n  let keys = [root];\n  let index = -1;\n  let edits = [];\n  let node = root;\n  let key = undefined;\n  let parent = undefined;\n  const path = [];\n  const ancestors = [];\n  /* eslint-enable no-undef-init */\n\n  do {\n    index++;\n    const isLeaving = index === keys.length;\n    const isEdited = isLeaving && edits.length !== 0;\n\n    if (isLeaving) {\n      key = ancestors.length === 0 ? undefined : path[path.length - 1];\n      node = parent;\n      parent = ancestors.pop();\n\n      if (isEdited) {\n        if (inArray) {\n          node = node.slice();\n          let editOffset = 0;\n\n          for (const [editKey, editValue] of edits) {\n            const arrayKey = editKey - editOffset;\n\n            if (editValue === null) {\n              node.splice(arrayKey, 1);\n              editOffset++;\n            } else {\n              node[arrayKey] = editValue;\n            }\n          }\n        } else {\n          node = { ...node };\n\n          for (const [editKey, editValue] of edits) {\n            node[editKey] = editValue;\n          }\n        }\n      }\n\n      index = stack.index;\n      keys = stack.keys;\n      edits = stack.edits;\n      inArray = stack.inArray;\n      stack = stack.prev;\n    } else if (parent) {\n      key = inArray ? index : keys[index];\n      node = parent[key];\n\n      if (node === null || node === undefined) {\n        continue;\n      }\n\n      path.push(key);\n    }\n\n    let result;\n\n    if (!Array.isArray(node)) {\n      var _enterLeaveMap$get, _enterLeaveMap$get2;\n\n      (0,_ast_mjs__WEBPACK_IMPORTED_MODULE_0__.isNode)(node) || (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_2__.devAssert)(false, `Invalid AST Node: ${(0,_jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_3__.inspect)(node)}.`);\n      const visitFn = isLeaving\n        ? (_enterLeaveMap$get = enterLeaveMap.get(node.kind)) === null ||\n          _enterLeaveMap$get === void 0\n          ? void 0\n          : _enterLeaveMap$get.leave\n        : (_enterLeaveMap$get2 = enterLeaveMap.get(node.kind)) === null ||\n          _enterLeaveMap$get2 === void 0\n        ? void 0\n        : _enterLeaveMap$get2.enter;\n      result =\n        visitFn === null || visitFn === void 0\n          ? void 0\n          : visitFn.call(visitor, node, key, parent, path, ancestors);\n\n      if (result === BREAK) {\n        break;\n      }\n\n      if (result === false) {\n        if (!isLeaving) {\n          path.pop();\n          continue;\n        }\n      } else if (result !== undefined) {\n        edits.push([key, result]);\n\n        if (!isLeaving) {\n          if ((0,_ast_mjs__WEBPACK_IMPORTED_MODULE_0__.isNode)(result)) {\n            node = result;\n          } else {\n            path.pop();\n            continue;\n          }\n        }\n      }\n    }\n\n    if (result === undefined && isEdited) {\n      edits.push([key, node]);\n    }\n\n    if (isLeaving) {\n      path.pop();\n    } else {\n      var _node$kind;\n\n      stack = {\n        inArray,\n        index,\n        keys,\n        edits,\n        prev: stack,\n      };\n      inArray = Array.isArray(node);\n      keys = inArray\n        ? node\n        : (_node$kind = visitorKeys[node.kind]) !== null &&\n          _node$kind !== void 0\n        ? _node$kind\n        : [];\n      index = -1;\n      edits = [];\n\n      if (parent) {\n        ancestors.push(parent);\n      }\n\n      parent = node;\n    }\n  } while (stack !== undefined);\n\n  if (edits.length !== 0) {\n    // New root\n    return edits[edits.length - 1][1];\n  }\n\n  return root;\n}\n/**\n * Creates a new visitor instance which delegates to many visitors to run in\n * parallel. Each visitor will be visited for each node before moving on.\n *\n * If a prior visitor edits a node, no following visitors will see that node.\n */\n\nfunction visitInParallel(visitors) {\n  const skipping = new Array(visitors.length).fill(null);\n  const mergedVisitor = Object.create(null);\n\n  for (const kind of Object.values(_kinds_mjs__WEBPACK_IMPORTED_MODULE_1__.Kind)) {\n    let hasVisitor = false;\n    const enterList = new Array(visitors.length).fill(undefined);\n    const leaveList = new Array(visitors.length).fill(undefined);\n\n    for (let i = 0; i < visitors.length; ++i) {\n      const { enter, leave } = getEnterLeaveForKind(visitors[i], kind);\n      hasVisitor || (hasVisitor = enter != null || leave != null);\n      enterList[i] = enter;\n      leaveList[i] = leave;\n    }\n\n    if (!hasVisitor) {\n      continue;\n    }\n\n    const mergedEnterLeave = {\n      enter(...args) {\n        const node = args[0];\n\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _enterList$i;\n\n            const result =\n              (_enterList$i = enterList[i]) === null || _enterList$i === void 0\n                ? void 0\n                : _enterList$i.apply(visitors[i], args);\n\n            if (result === false) {\n              skipping[i] = node;\n            } else if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined) {\n              return result;\n            }\n          }\n        }\n      },\n\n      leave(...args) {\n        const node = args[0];\n\n        for (let i = 0; i < visitors.length; i++) {\n          if (skipping[i] === null) {\n            var _leaveList$i;\n\n            const result =\n              (_leaveList$i = leaveList[i]) === null || _leaveList$i === void 0\n                ? void 0\n                : _leaveList$i.apply(visitors[i], args);\n\n            if (result === BREAK) {\n              skipping[i] = BREAK;\n            } else if (result !== undefined && result !== false) {\n              return result;\n            }\n          } else if (skipping[i] === node) {\n            skipping[i] = null;\n          }\n        }\n      },\n    };\n    mergedVisitor[kind] = mergedEnterLeave;\n  }\n\n  return mergedVisitor;\n}\n/**\n * Given a visitor instance and a node kind, return EnterLeaveVisitor for that kind.\n */\n\nfunction getEnterLeaveForKind(visitor, kind) {\n  const kindVisitor = visitor[kind];\n\n  if (typeof kindVisitor === 'object') {\n    // { Kind: { enter() {}, leave() {} } }\n    return kindVisitor;\n  } else if (typeof kindVisitor === 'function') {\n    // { Kind() {} }\n    return {\n      enter: kindVisitor,\n      leave: undefined,\n    };\n  } // { enter() {}, leave() {} }\n\n  return {\n    enter: visitor.enter,\n    leave: visitor.leave,\n  };\n}\n/**\n * Given a visitor instance, if it is leaving or not, and a node kind, return\n * the function the visitor runtime should call.\n *\n * @deprecated Please use `getEnterLeaveForKind` instead. Will be removed in v17\n */\n\n/* c8 ignore next 8 */\n\nfunction getVisitFn(visitor, kind, isLeaving) {\n  const { enter, leave } = getEnterLeaveForKind(visitor, kind);\n  return isLeaving ? leave : enter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/graphql/language/visitor.mjs\n");

/***/ })

};
;